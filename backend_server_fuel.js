// <PERSON>
// Sep 25, 2024
// this TCP server for device connectiions
// |------> Jimi handler -----> destination server
// |------> Teltonika handler -----> destination server
// |------> Amwell handler -----> destination server
// |------> iStartek handler -----> destination server
// |------> db.js ------> Sqlite3 database
// |------> api.js <------> webhost.js <------> index.html

// npm install body-parser
// npm install cors
// npm install decache --save-dev
// pm2 start backend_server.js --node-args=' --max-old-space-size=200'


const net = require('net');

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const decache = require('decache');

const app = express();
const PORT_API = 3005;
const { router, createCommandHandler, createRawDataHandler } = require('./api_fuel.js');

const TeltonikaHandler = require('./handler_teltonika_fuel.js');
const JimiHandler = require('./handler_jimi_fuel.js');

const projectTcpPorts = [
    { name: 'Loconav-Teltonika', port: 5035, },
    { name: 'TracksolidPro-Jimi', port: 21101, },
]

const tcpServers = [];

let deviceConnections = 0;
let connectionId = 1;
const IDLE_TIMEOUT = 30 * 1000;
const MAX_BUFFER_SIZE = 30 * 1024; // 5 KB buffer size limit
const MAX_MEMORY_RESTART = 100; // 200 MB memory limit

const TELTONIKA_START_BIT = '000f';
const JIMI_START_BIT = '7878';

// Create a Map to store the deviceSocket and its associated handler
const socketToHandler = new Map();
const commandAndResponse = new Map();
const imeiToHandler = new Map();

// Function to handle incoming TCP connections
function handleTcpConnection(deviceSocket) {
    deviceConnections++;
    deviceSocket.connectionId = connectionId++;

    let buffer = Buffer.alloc(0);

    deviceSocket.on('data', async (data) => {
        // Append incoming data to the buffer
        buffer = Buffer.concat([buffer, data]);

        // Check if the buffer size exceeds the maximum allowed size
        if (buffer.length > MAX_BUFFER_SIZE) {
            //console.error(`Buffer size exceeded for connection ${connectionId}. Closing socket.`);
            deviceSocket.destroy();
            return;
        }

        // Check if the deviceSocket already has an associated handler
        if (socketToHandler.has(deviceSocket)) {
            const handler = socketToHandler.get(deviceSocket);
            handler.handle(deviceSocket, data, commandAndResponse);

        } else {
            // Determine the handler based on the start bit
            const startBit = data.slice(0, 2).toString('hex');
            let handler;
            let handlerType;

            switch (startBit) {
                case TELTONIKA_START_BIT:
                    handler = new TeltonikaHandler();
                    handlerType = 'Teltonika';
                    break;
                case JIMI_START_BIT:
                    handler = new JimiHandler();
                    handlerType = 'Jimi';
                    break;
                default:
                    console.error(`Invalid message received: ${data.toString('hex')}`);
                    deviceSocket.destroy();
                    return; // Exit the function since the message is invalid
            }

            // Store the association between deviceSocket and handler instance
            socketToHandler.set(deviceSocket, handler);
            handler.handle(deviceSocket, data, commandAndResponse);

            if (handler.imei) {
                imeiToHandler.set(handler.imei, { handler, handlerType });
            }
        }
    });

    deviceSocket.setTimeout(IDLE_TIMEOUT, () => {
        deviceSocket.destroy()
    });
    deviceSocket.on('error', (error) => {
        deviceSocket.destroy()
    });
    deviceSocket.on('close', () => {
        deviceSocket.removeAllListeners();
        const handler = socketToHandler.get(deviceSocket);
        if (handler) {
            handler.cleanup();
            imeiToHandler.delete(handler.imei);
        }
        socketToHandler.delete(deviceSocket);
        deviceConnections--;
    });
}

// Function to log memory usage and active socket count
function logStats() {
    const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // Convert to MB
    console.log(`Connections: ${deviceConnections}; Memory Usage RSS : ${memoryUsage.toFixed(2)} MB`);

    if (memoryUsage > MAX_MEMORY_RESTART * 0.5) clearHandlerCache();
    if (memoryUsage > MAX_MEMORY_RESTART) {
        console.log(`${memoryUsage.toFixed(2)} Restarting server...`);
        shutdown();
    }
}
setInterval(logStats, 10 * 1000);


// Start TCP servers with wrapper to pass server name and port
projectTcpPorts.forEach(portInfo => {
    const server = net.createServer((deviceSocket) => handleTcpConnection(deviceSocket));
    server.listen(portInfo.port, () => {
        console.log(`TCP Server ${portInfo.name} is running on port ${portInfo.port}`);
    });
    tcpServers.push(server);
});


// API Implementation
app.use(cors());

// Add error handling for JSON parsing
app.use(bodyParser.json({
    verify: (req, res, buf, encoding) => {
        try {
            JSON.parse(buf);
        } catch (e) {
            res.status(400).json({ error: 'Invalid JSON' });
            throw new Error('Invalid JSON');
        }
    }
}));
app.use(bodyParser.urlencoded({ extended: false }));
app.use(express.json());

// Use the router from api.js for all API endpoints
let activeRequests = 0;
app.use((req, res, next) => {
    const startTime = Date.now();
    activeRequests++;
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    console.log(`Active Requests: ${activeRequests}, ${req.url}, from: ${clientIp}`);

    const onFinish = () => {
        const duration = Date.now() - startTime;
        activeRequests--;
        console.log(`Active Requests: ${activeRequests}, ${req.url} completed in ${duration} ms, from: ${clientIp}`);
    };

    // Attach listeners for finish and close events
    res.on('finish', onFinish);

    next();
});

// Use the router from api.js for all API endpoints
app.use('/api', router);

// Add the send command API endpoint
app.get('/api/send-command', createCommandHandler(commandAndResponse, imeiToHandler));

// Add the raw data API endpoint
app.get('/api/device/raw', createRawDataHandler(imeiToHandler));

const apiServer = app.listen(PORT_API, () => {
    console.log(`API server is running on port ${PORT_API}`);
});



// Clear cache for handlers
function clearHandlerCache() {
    decache('./handler_jimi.js');
    decache('./handler_teltonika.js');
    decache('./handler_amwell.js');
    decache('./handler_istartek.js');
    decache('./api.js');
}


// Graceful shutdown
const shutdown = () => {
    apiServer.close();

    // Close all TCP servers
    tcpServers.forEach(server => server.close(() => {
        socketToHandler.forEach((handler, deviceSocket) => {
            deviceSocket.end();
        });
        setTimeout(() => {
            process.exit(0);
        }, 10 * 1000);
    })
    );
};

// Handle termination signals
process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);