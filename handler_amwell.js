// npm i async-mutex

const net = require('net');
const { getDbInstance, createTableForIMEI, imeiLastRecords } = require('./db.js');
const db = getDbInstance();
const { Mutex } = require('async-mutex');
const dbWriteLock = new Mutex();

const destinations = [
    { name: 'Fleetnav-Amwell', host: '*************', port: 5335 },
    //{ name: 'iNav-Office', host: '**************', port: 6300 },
];

const fullCommands = [
    //'SENDINGTIME 888888 0',
    //'*SPJLX*P:888888*F:#',
    //'*SPJLX*P:888888*B:88#',
    //'*SPJLX*P:888888*U:gps.inavcloud.com,5335,1,#',
    //'SENDINGTIME 753869 0',
    //'*SPJLX*P:753869*F:#',
    //'*SPJLX*P:753869*B:88#',
    //'*SPJLX*P:753869*U:gps.inavcloud.com,5335,1,#',
    //'LTFRBIP ltfrb.inavcloud.com,5542,1',
];

let delayedImeis = [];
const lastImeiData = new Map();
const lastCommandSentTime = new Map(); // Track when commands were last sent to each IMEI
const imeiErrorCount = new Map(); // Track error counts per IMEI

let destinationConnectionId = 1;
let logCount = 10;
const IDLE_TIMEOUT = 30 * 1000;
const SQL_BATCH_INTERVAL = 10 * 1000;
const COMMAND_COOLDOWN = 120 * 1000; // 2 minutes in milliseconds
const MAX_ERRORS_PER_IMEI = 10; // Maximum number of errors to log per IMEI
const imeiLatLngTimeAcc = new Map();

const options = {
    timeZone: 'Asia/Manila',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
};

// Constants
const START_BIT = '2929';
const END_BIT = '0d';
const LOGIN_MESSAGE_TYPE = 0xB1;
const GPS_MESSAGE_TYPE = 0x80;
const DATA_MESSAGE_TYPE = 0xA3;
const ALARM_MESSAGE_TYPE = 0x82;
const COMMAND_MESSAGE_TYPE = 0x3A;
const COMMAND_RESPONSE_TYPE_1 = 0x85;
const COMMAND_RESPONSE_TYPE_2 = 0x84;

// 29298200233accc01a240821115944010390151225660100000306fb00000000000001000000a40d
// 29298e00282aab8913240820062755016015811201550300300073fb0169087fff0f000000000000000000220d
// 2929a3002c3acaaa29240822002016010391411225589400080022fb0009ce7ffd5300000800000000000021020082830d
// 29298500283ACC883A240910050716014369381210224300000000F300A189FFFD5F00000100000000003A0D0D29298400273ACC883A2869702C302C312C6770732E696E6176636C6F75642E636F6D2C353333352C31298A0D

// Message types that need response
const MESSAGE_NEED_RESPONSE_TYPE = [
    LOGIN_MESSAGE_TYPE,
    ALARM_MESSAGE_TYPE
];

class AmwellHandler {
    constructor() {

        this.destinationSockets = new Map();
        this.sqlBatch = [];
        this.sqlBatchTimer = null;

        this.imei = '';
        this.data = '';
        this.ltfrbData = '';

        this.initializeSQLBatch();
    }

    initializeSQLBatch() {
        this.sqlBatchTimer = setInterval(this.sqlLogBatch.bind(this), SQL_BATCH_INTERVAL);
    }

    async sqlLogBatch() {
        if (this.sqlBatch.length > 0 && this.imei) {
            await dbWriteLock.runExclusive(() => {
                return new Promise((resolve, reject) => {
                    createTableForIMEI(db, this.imei);  // Ensure table exists

                    const sanitizedImei = this.imei.replace(/[^a-zA-Z0-9_]/g, '');
                    const query = `INSERT INTO gps_data_${sanitizedImei} (
                        serverTimestamp,
                        gpsTimestamp,
                        latitude,
                        longitude,
                        speed,
                        course,
                        satelliteCount,
                        accCode
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

                    db.serialize(() => {
                        db.run("BEGIN TRANSACTION");
                        const stmt = db.prepare(query);

                        try {
                            for (const message of this.sqlBatch) {
                                if (message.messageType !== LOGIN_MESSAGE_TYPE) {
                                    stmt.run(message.data, (err) => {
                                        if (err) {
                                            console.error(`Error inserting data for IMEI ${this.imei}:`, err.message);
                                        }
                                    });
                                }
                            }

                            stmt.finalize((err) => {
                                if (err) {
                                    console.error('Error finalizing statement:', err.message);
                                    db.run("ROLLBACK");
                                    reject(err);
                                } else {
                                    db.run("COMMIT", (err) => {  // Commit transaction
                                        if (err) {
                                            console.error('Error committing transaction:', err.message);
                                            db.run("ROLLBACK");
                                            reject(err);
                                        } else {
                                            this.sqlBatch = []; // Clear batch after successful commit
                                            resolve();
                                        }
                                    });
                                }
                            });
                        } catch (err) {
                            console.error('Error in batch processing:', err);
                            stmt.finalize();
                            db.run("ROLLBACK");
                            reject(err);
                        }
                    });
                });
            }).catch((err) => {
                console.error('Database write failed:', err);
            });
        }
    }

    connectToDestinationSockets() {
        for (const destination of destinations) {
            const socket = new net.Socket();

            socket.connect(destination.port, destination.host, () => {
                socket.destination = destination;
                socket.connectionId = destinationConnectionId++;
                this.destinationSockets.set(socket.connectionId, socket);
                //if (this.imei === '54201413' || this.imei === '54201488') console.log(`${this.imei} connected to ${destination.name}[${socket.connectionId}]`);
            });

            socket.on('error', err => {
                if (logCount !== 0) {
                    console.log(`Error connecting to ${destination.name}: ${err.message}`);
                    logCount--;
                }
                socket.destroy();
            });

            socket.on('close', () => {
                socket.removeAllListeners();
                this.destinationSockets.delete(socket.connectionId);
            })

            socket.setTimeout(IDLE_TIMEOUT, () => {
                socket.destroy()
            });
        }
    }

    async handle(deviceSocket, data, commandAndResponse) {

        //const serverTimestamp = new Date().toLocaleString('en-CA', options).replace(',', '');
        const serverTimestamp = new Date().toISOString();

        try {
            const parsedMessage = AmwellHandler.parseMessage(data);
            //console.log('Parsed message:', parsedMessage);

            if (parsedMessage.imei) this.imei = parsedMessage.imei;
            if (parsedMessage.data) this.data = parsedMessage.data;
            if (parsedMessage.ltfrbData) this.ltfrbData = parsedMessage.ltfrbData;

            // Store last records
            const existingData = imeiLastRecords.get(this.imei) || {};
            const updatedData = {
                imei: this.imei,
                serverTimestamp: serverTimestamp,
                gpsTimestamp: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.gpsTimestamp : parsedMessage.gpsTimestamp,
                latitude: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.latitude : parsedMessage.latitude,
                longitude: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.longitude : parsedMessage.longitude,
                speed: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.speed : parsedMessage.speed,
                course: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.course : parsedMessage.course,
                satelliteCount: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.satelliteCount : parsedMessage.satelliteCount,
                accCode: parsedMessage.messageType === LOGIN_MESSAGE_TYPE ? existingData.accCode : parsedMessage.accCode
            };

            imeiLastRecords.set(this.imei, updatedData);

            // Add data to SQL batch
            let latLngTimeAcc = imeiLatLngTimeAcc.get(this.imei) || [0, 0, 0, 0];
            const dist = Math.sqrt(Math.pow(latLngTimeAcc[0] - parsedMessage.latitude, 2) + Math.pow(latLngTimeAcc[1] - parsedMessage.longitude, 2)) * 111 * 1000;
            const timeDiff = (new Date(parsedMessage.gpsTimestamp) - new Date(latLngTimeAcc[2])) / 1000;
            const isAccChanged = (parsedMessage.accCode !== latLngTimeAcc[3]);
            const isLatLng = (parsedMessage.latitude !== 0 && parsedMessage.longitude !== 0);

            // Insert GPS or DATA message into SQLite database
            if (
                ((parsedMessage.messageType === GPS_MESSAGE_TYPE || parsedMessage.messageType === DATA_MESSAGE_TYPE) &&
                    parsedMessage.gpsTimestamp &&
                    isLatLng &&
                    (dist > 50 || timeDiff > 3600 || isAccChanged)
                ) || !imeiLatLngTimeAcc.has(this.imei)
            ) {
                this.sqlBatch.push({
                    messageType: parsedMessage.messageType,
                    data: [
                        serverTimestamp,
                        parsedMessage.gpsTimestamp,
                        parsedMessage.latitude,
                        parsedMessage.longitude,
                        parsedMessage.speed,
                        parsedMessage.course,
                        parsedMessage.satelliteCount,
                        parsedMessage.accCode
                    ]
                });
                imeiLatLngTimeAcc.set(this.imei, [
                    parseFloat(parsedMessage.latitude),
                    parseFloat(parsedMessage.longitude),
                    parsedMessage.gpsTimestamp,
                    parsedMessage.accCode
                ]);
            }

            // Check if destination sockets already exist for this IMEI
            if (this.destinationSockets && this.destinationSockets.size === 0) {
                await new Promise((resolve) => {
                    this.connectToDestinationSockets();

                    // Check if all destination sockets are connected
                    const intervalId = setInterval(() => {
                        if (this.destinationSockets.size === destinations.length) {
                            clearInterval(intervalId);
                            clearTimeout(timeoutId);
                            // Only resolve the promise here, after sockets have connected
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to reject the promise if the condition isn't met in time
                    const timeoutId = setTimeout(() => {
                        clearInterval(intervalId);
                        resolve();
                    }, 5 * 1000)
                });
            }

            // Forward device data and destination response
            if (this.destinationSockets && this.destinationSockets.size > 0) {
                this.destinationSockets.forEach(socket => {

                    // Send device data to destination
                    if (socket && socket.writable) {
                        //if (this.imei === '54201488') console.log(`Send ${this.imei}[${deviceSocket.connectionId}] data to ${socket.destination.name}[${socket.connectionId}] ${data.toString('hex')}`);
                        socket.write(data, err => {
                            if (err) {
                                socket.destroy();
                                this.destinationSockets.delete(socket.connectionId);
                                console.log(`Error sending ${this.imei} data to ${socket.destination.name}[${socket.connectionId}]: ${err.message}`);
                            }
                        });
                    } else {
                        this.destinationSockets.delete(socket.connectionId);
                        return; // Skip to the next socket
                    }

                    // Send destination response to device
                    socket.removeAllListeners('data');
                    socket.on('data', (response) => {
                        if (deviceSocket && deviceSocket.writable) {
                            //console.log(`${this.imei} received response from ${socket.destination.name}[${socket.connectionId}]`);
                            deviceSocket.write(response, err => {
                                if (err) {
                                    deviceSocket.destroy();
                                    //console.log(`Error sending response to ${this.imei} [${deviceSocket.connectionId}]`);
                                }
                            });
                        } else {
                            deviceSocket.destroy();
                            //console.log(`Error ${this.imei} not writable [${deviceSocket.connectionId}]`);
                        }
                    });
                });
            }

            // Send response to device
            if (this.isMessageNeedResponse(parsedMessage)) {
                const responseMessage = this.createResponse(parsedMessage.checksum, parsedMessage.messageType, parsedMessage.sequenceNumber);
                //console.log(`Response to ${this.imei} [${deviceSocket.connectionId}]:`, responseMessage);
                if (deviceSocket && deviceSocket.writable) {
                    deviceSocket.write(responseMessage, (err) => {
                        if (err) {
                            deviceSocket.destroy();
                            console.log(`Error sending my response to ${this.imei}`);
                        }
                    });
                }
                // Add delay before sending command
                await new Promise(resolve => setTimeout(resolve, 100)); // add delay
            }

            // Send command to device
            if (commandAndResponse && commandAndResponse.has(this.imei)) {
                const commandData = commandAndResponse.get(this.imei);
                const fullCommand = commandData.command.startsWith('*') ? `${commandData.command}#` : commandData.command;
                if (!commandData.response && parsedMessage.messageType !== LOGIN_MESSAGE_TYPE) {
                    const currentTime = Date.now();
                    const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                    // Check if the cooldown period has passed since the last command was sent
                    if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                        const commandMessage = AmwellHandler.constructCommandMessage(fullCommand, this.imei);
                        console.log(`Send command to ${this.imei} [${deviceSocket.connectionId}]:`, commandData.command);

                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending command to ${this.imei}`);
                                }
                            });

                            // Update the last sent time for this IMEI
                            lastCommandSentTime.set(this.imei, currentTime);
                        }
                    } else {
                        console.log(`Command to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN/1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                    }
                }
            }

            // Store command response
            if (parsedMessage.responseToCommand) {
                const commandData = commandAndResponse.get(this.imei);
                if (commandData) {
                    commandData.response = parsedMessage.responseToCommand;
                    commandAndResponse.set(this.imei, commandData);
                }
            }

            // Send troubleshooting command to delayed device
            let isGsmReset = false;
            if (lastImeiData.get(this.imei) === data) isGsmReset = true;
            lastImeiData.set(this.imei, this.data);

            if (parsedMessage.gpsTimestamp) {
                const isDelay = (new Date() - new Date(parsedMessage.gpsTimestamp)) > 1 * 60 * 60 * 1000;
                if (isDelay) delayedImeis.push(this.imei);
            }

            if (delayedImeis.includes(this.imei) || isGsmReset) {
                for (let i = 0; i < fullCommands.length; i++) {
                    const commandMessage = AmwellHandler.constructCommandMessage(fullCommands[i], this.imei);
                    //console.log(`Send troubleshooting command to ${this.imei} [${deviceSocket.connectionId}]:`, fullCommands[i]);
                    if (deviceSocket && deviceSocket.writable) {
                        deviceSocket.write(commandMessage, (err) => {
                            if (err) {
                                deviceSocket.destroy();
                                console.error(`Error sending troubleshooting command to ${this.imei}:`, err);
                            }
                        });
                    }
                    // Add delay before sending the next command
                    await new Promise(resolve => setTimeout(resolve, 100)); // add delay
                }
                delayedImeis = delayedImeis.filter(imei => imei !== this.imei);
            }

        } catch (error) {
            // Get current error count for this IMEI or initialize to 0
            const errorCount = imeiErrorCount.get(this.imei) || 0;

            // Only log if we haven't reached the maximum number of errors for this IMEI
            if (errorCount < MAX_ERRORS_PER_IMEI) {
                console.error(`Amwell Handle Error [${this.imei}]:`, error.message);
                // Increment the error count for this IMEI
                imeiErrorCount.set(this.imei, errorCount + 1);
            }
        }
    }

    cleanup() {
        if (this.sqlBatchTimer) {
            clearInterval(this.sqlBatchTimer);
        }

        if (this.sqlBatch.length > 0) {
            return this.sqlLogBatch()
                .catch(err => console.error('Error during final batch save:', err))
                .finally(() => {
                    this.destinationSockets.forEach(socket => {
                        socket.destroy();
                    });
                    this.destinationSockets.clear();
                });
        } else {
            this.destinationSockets.forEach(socket => {
                socket.destroy();
            });
            this.destinationSockets.clear();
            return Promise.resolve();
        }
    }

    // HELPER FUNCTIONS
    static getMessageSegment = (message, start, end) => message.slice(start, end).toString('hex');
    static getAsciiSegment = (message, start, end) => message.slice(start, end).toString('ascii');
    isMessageNeedResponse = (parsedMessage) => MESSAGE_NEED_RESPONSE_TYPE.includes(parsedMessage.messageType);


    // Parse messages
    static parseMessage = (message) => {
        const messageType = message[2];

        switch (messageType) {
            case LOGIN_MESSAGE_TYPE:
                return {
                    ...AmwellHandler.parseLoginMessage(message),
                    messageType
                };
            case GPS_MESSAGE_TYPE:
                return {
                    ...AmwellHandler.parseGpsMessage(message),
                    messageType
                };
            case DATA_MESSAGE_TYPE:
                return {
                    ...AmwellHandler.parseDataMessage(message),
                    messageType
                };
            case COMMAND_RESPONSE_TYPE_2:
                return {
                    ...AmwellHandler.parseCommandResponse(message),
                    messageType
                }
            default:
                throw new Error(`Amwell Unparsed ${message.toString('hex')}`);
        }
    };

    // Parse login message
    // 2929b100072d93a82b0c870d
    static parseLoginMessage = (message) => {

        const imeiHex = message.slice(5, 9);
        const imei = `${('0' + imeiHex[0].toString(10)).slice(-2)}${('0' + (imeiHex[1] - 0x80).toString(10)).slice(-2)}${('0' + (imeiHex[2] - 0x80).toString(10)).slice(-2)}${('0' + imeiHex[3].toString(10)).slice(-2)}`;
        const packetLength = AmwellHandler.getMessageSegment(message, 3, 5);

        const sequenceNumber = AmwellHandler.getMessageSegment(message, 9, 10);
        const checksum = AmwellHandler.getMessageSegment(message, 10, 11);
        const endBit = AmwellHandler.getMessageSegment(message, 11, 12);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `imei:${imei}; packetLength:${packetLength}; sequenceNumber:${sequenceNumber}`;

        return {
            imei,
            data,
            sequenceNumber,
            checksum
        };
    };

    // Function to create a server response message
    // Login: 2929B100070A9F95380C820D
    // Response: 292921000582B10C1B0D
    createResponse = (sourceChecksum, sourceMessageType, sequenceNumber) => {
        const startBit = Buffer.from('2929', 'hex');
        const messageType = Buffer.from('21', 'hex');
        const packetLength = Buffer.from('0005', 'hex');
        //console.log(sourceChecksum, sourceMessageType, sequenceNumber);

        const sourceChecksumHex = Buffer.from(sourceChecksum, 'hex');
        const sourceMessageTypeHex = Buffer.from(sourceMessageType.toString(16), 'hex');
        const sequenceNumberHex = Buffer.from(sequenceNumber.toString(16).padStart(2, '0'), 'hex');
        const messageWithoutCrc = Buffer.concat([startBit, messageType, packetLength, sourceChecksumHex, sourceMessageTypeHex, sequenceNumberHex]);

        // Calculate checksum
        const checksum = Buffer.from(AmwellHandler.checksum(messageWithoutCrc).toString(16).padStart(2, '0'), 'hex');
        const endBit = Buffer.from('0d', 'hex');

        return Buffer.concat([messageWithoutCrc, checksum, endBit]);
    };


    // Parse GPS message
    // 29298000283887b71e230418162603014369341210224300000061fb0000007fff5d000000000000000f00860d
    static parseGpsMessage(message) {

        const imeiHex = message.slice(5, 9);
        const imei = `${('0' + imeiHex[0].toString(10)).slice(-2)}${('0' + (imeiHex[1] - 0x80).toString(10)).slice(-2)}${('0' + (imeiHex[2] - 0x80).toString(10)).slice(-2)}${('0' + imeiHex[3].toString(10)).slice(-2)}`;
        const packetLength = AmwellHandler.getMessageSegment(message, 3, 5);
        //console.log(imei, packetLength);

        const year = parseInt(AmwellHandler.getMessageSegment(message, 9, 10)) + 2000;
        const month = parseInt(AmwellHandler.getMessageSegment(message, 10, 11) - 1);
        const day = parseInt(AmwellHandler.getMessageSegment(message, 11, 12));
        const hour = parseInt(AmwellHandler.getMessageSegment(message, 12, 13));
        const minute = parseInt(AmwellHandler.getMessageSegment(message, 13, 14));
        const second = parseInt(AmwellHandler.getMessageSegment(message, 14, 15));
        let timestamp = new Date(Date.UTC(year, month, day, hour, minute, second));
        //console.log(imei, timestamp, new Date(), timestamp.getTime() - new Date().getTime());

        // Check if the timezone offset is not 0 (indicating it's not in UTC)
        if (timestamp.getTime() > new Date().getTime()) {
            const adjustedTimestamp = timestamp.getTime() - (8 * 60 * 60 * 1000);
            timestamp = new Date(adjustedTimestamp);
        }
        const gpsTimestamp = timestamp.toISOString().replace('Z', '').replace('T', ' ').split('.')[0];
        //console.log(gpsTimestamp);

        const latitudeCode = parseInt(AmwellHandler.getMessageSegment(message, 15, 19));
        const latitudeDegrees = Math.floor(latitudeCode / 100000);
        const latitudeMinutes = (latitudeCode % 100000) / 1000;
        const latitude = parseFloat((latitudeDegrees + latitudeMinutes / 60).toFixed(6));
        const longitudeCode = parseInt(AmwellHandler.getMessageSegment(message, 19, 23));
        const longitudeDegrees = Math.floor(longitudeCode / 100000);
        const longitudeMinutes = (longitudeCode % 100000) / 1000;
        const longitude = parseFloat((longitudeDegrees + longitudeMinutes / 60).toFixed(6));
        const speed = parseInt(AmwellHandler.getMessageSegment(message, 23, 25));
        const course = parseInt(AmwellHandler.getMessageSegment(message, 25, 27));
        //console.log(imei, latitude, longitude, speed, course);

        const gpsStatusCode = parseInt(AmwellHandler.getMessageSegment(message, 27, 28), 16);
        const isPositionFixed = (gpsStatusCode & 0b10000000) >>> 7;
        const isPowerConnected = (gpsStatusCode & 0b11000) >>> 3 === 0b11 ? true : false;
        const mileage = parseInt(AmwellHandler.getMessageSegment(message, 28, 31), 16);
        const statusCode = parseInt(AmwellHandler.getMessageSegment(message, 31, 35), 16);
        const accCode = statusCode >>> 31 === 0b1 ? 0 : 1;
        const statusCodeByte1 = parseInt(AmwellHandler.getMessageSegment(message, 31, 32), 16);
        //console.log(imei, gpsStatusCode.toString(2), isPositionFixed, isPowerConnected, mileage, statusCodeByte1.toString(2).padStart(8, '0'), accCode);

        const stateV1_2Code = parseInt(AmwellHandler.getMessageSegment(message, 35, 37), 16);
        const satelliteCount = parseInt(AmwellHandler.getMessageSegment(message, 37, 38), 16);
        const stateV4Code = parseInt(AmwellHandler.getMessageSegment(message, 38, 39), 16);
        const stateV5_7Code = parseInt(AmwellHandler.getMessageSegment(message, 39, 42), 16);
        const stateV8Code = parseInt(AmwellHandler.getMessageSegment(message, 42, 43), 16);
        //console.log(stateV1_2Code, satelliteCount, stateV4Code, stateV5_7Code, stateV8Code);

        const checksum = AmwellHandler.getMessageSegment(message, 43, 44);
        const endBit = AmwellHandler.getMessageSegment(message, 44, 45);
        //console.log(checksum, endBit);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        // Generate LTFRB strings
        const gpsId = '88' + imei.slice(-8);
        const time = gpsTimestamp.slice(-8); // 09:51:10
        const date = gpsTimestamp.slice(0, 10); // 2024-07-25
        const ltfrbData = `ID#${gpsId}+TM#${time}+DT#${date}+LT#${latitude}+LN#${longitude}+AT#$0+SP#${speed}+CO#${course}\r\n`;
        //console.log(imei, ltfrbData);

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; course=${course}; isPositionFixed=${isPositionFixed}; isPowerConnected=${isPowerConnected}; accCode=${accCode}; ${ltfrbData}`;

        return {
            imei,
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            ltfrbData
        };
    }

    // Parse Data message
    // 2929a3002c28CB8241240824113930016244421203603900040012fb0087be7ffc5800000407000000000021020081170d
    // 2929a3002c3887d763240824113930016244421203603900040012fb0087be7ffc5800000407000000000021020081170d
    // Super: 2929A3002C3ACC8C562409100331570152979212035666000000007B0098D4FFFD5F00000000000000000021020075B00D
    static parseDataMessage = (message) => {

        const imeiHex = message.slice(5, 9);
        const imei = `${('0' + imeiHex[0].toString(10)).slice(-2)}${('0' + (imeiHex[1] - 0x80).toString(10)).slice(-2)}${('0' + (imeiHex[2] - 0x80).toString(10)).slice(-2)}${('0' + imeiHex[3].toString(10)).slice(-2)}`;
        // validate imei if only contains numbers
        if (!imei.match(/^[0-9]+$/)) {
            throw new Error('Invalid IMEI');
        }
        const packetLength = AmwellHandler.getMessageSegment(message, 3, 5);
        //console.log(imei, packetLength);

        const year = parseInt(AmwellHandler.getMessageSegment(message, 9, 10)) + 2000;
        const month = parseInt(AmwellHandler.getMessageSegment(message, 10, 11) - 1);
        const day = parseInt(AmwellHandler.getMessageSegment(message, 11, 12));
        const hour = parseInt(AmwellHandler.getMessageSegment(message, 12, 13));
        const minute = parseInt(AmwellHandler.getMessageSegment(message, 13, 14));
        const second = parseInt(AmwellHandler.getMessageSegment(message, 14, 15));
        let timestamp = new Date(Date.UTC(year, month, day, hour, minute, second));
        //console.log(imei, timestamp, new Date(), timestamp.getTime() - new Date().getTime(), new Date().getTimezoneOffset() * 60 * 1000);

        // Check if the timezone offset is not 0 (indicating it's not in UTC)
        if (timestamp.getTime() > new Date().getTime()) {
            const adjustedTimestamp = timestamp.getTime() - (8 * 60 * 60 * 1000);
            timestamp = new Date(adjustedTimestamp);
        }
        const gpsTimestamp = timestamp.toISOString().replace('Z', '').replace('T', ' ').split('.')[0];
        //console.log(gpsTimestamp);

        const latitudeCode = parseInt(AmwellHandler.getMessageSegment(message, 15, 19));
        const latitudeDegrees = Math.floor(latitudeCode / 100000);
        const latitudeMinutes = (latitudeCode % 100000) / 1000;
        const latitude = parseFloat((latitudeDegrees + latitudeMinutes / 60).toFixed(6));
        const longitudeCode = parseInt(AmwellHandler.getMessageSegment(message, 19, 23));
        const longitudeDegrees = Math.floor(longitudeCode / 100000);
        const longitudeMinutes = (longitudeCode % 100000) / 1000;
        const longitude = parseFloat((longitudeDegrees + longitudeMinutes / 60).toFixed(6));
        const speed = parseInt(AmwellHandler.getMessageSegment(message, 23, 25));
        const course = parseInt(AmwellHandler.getMessageSegment(message, 25, 27));
        //console.log(imei, latitude, longitude, speed, course);

        const gpsStatusCode = parseInt(AmwellHandler.getMessageSegment(message, 27, 28), 16);
        const isPositionFixed = (gpsStatusCode & 0b10000000) >>> 7;
        const isPowerConnected = (gpsStatusCode & 0b11000) >>> 3 === 0b11 ? true : false;
        const mileage = parseInt(AmwellHandler.getMessageSegment(message, 28, 31), 16);
        const statusCode = parseInt(AmwellHandler.getMessageSegment(message, 31, 35), 16);
        const accCode = statusCode >>> 31 === 0b1 ? 0 : 1;
        //console.log(imei, gpsStatusCode.toString(2), isPositionFixed, isPowerConnected, mileage, accCode);

        const stateV1_2Code = parseInt(AmwellHandler.getMessageSegment(message, 35, 37), 16);
        const satelliteCount = parseInt(AmwellHandler.getMessageSegment(message, 37, 38), 16);
        const stateV4Code = parseInt(AmwellHandler.getMessageSegment(message, 38, 39), 16);
        const stateV5_7Code = parseInt(AmwellHandler.getMessageSegment(message, 39, 42), 16);
        const stateV8Code = parseInt(AmwellHandler.getMessageSegment(message, 42, 43), 16);
        //console.log(stateV1_2Code, satelliteCount, stateV4Code, stateV5_7Code, stateV8Code);

        const payloadLength = 4; // parseInt(AmwellHandler.getMessageSegment(message, 43, 45), 16);
        //console.log(payloadLength);

        const checksum = AmwellHandler.getMessageSegment(message, 43 + payloadLength, 44 + payloadLength);
        const endBit = AmwellHandler.getMessageSegment(message, 44 + payloadLength, 45 + payloadLength);
        //console.log(checksum, endBit);

        if (endBit !== END_BIT) {
            //throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; course=${course}; isPositionFixed=${isPositionFixed}; isPowerConnected=${isPowerConnected}; accCode=${accCode}`;

        return {
            imei,
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode
        };
    }

    // Parse Alarm message
    // 29298200232aab89132408201846330160028012013419000001318000000000000000000000b60d
    static parseAlarmMessage(message) {
        return alarm;
    }

    // Function to construct command message
    // IMEI: 54217625
    // Command : *SPJLX*P:753869*C:#
    // 29293a00193695cc192a53504a4c582a503a3735333836392a433a23140d

    // Undefined response from Amwell : 69474778
    // 2929a3002c3acc8147240824035605010419821225707400370171fb000c9a7ffd5f000037010000000000210200822a0d
    static constructCommandMessage = (command, deviceId) => {

        const startBit = Buffer.from(START_BIT, 'hex');
        const commandType = Buffer.from([COMMAND_MESSAGE_TYPE], 'hex');
        const commandLength = Buffer.from([command.length + 4 + 2]);

        // Convert deviceId to hex format
        const deviceIdHex = Buffer.from([
            parseInt(deviceId.slice(0, 2), 10),
            parseInt(deviceId.slice(2, 4), 10) + 0x80,
            parseInt(deviceId.slice(4, 6), 10) + 0x80,
            parseInt(deviceId.slice(6, 8), 10)
        ]);

        // Construct the message without CRC
        const messageWithoutCrc = Buffer.concat([
            startBit,
            commandType,
            Buffer.from([0x00]),
            commandLength,
            deviceIdHex,
            Buffer.from(command, 'utf8')
        ]);

        const checksumBuffer = Buffer.from([AmwellHandler.checksum(messageWithoutCrc)]);
        const finalMessage = Buffer.concat([messageWithoutCrc, checksumBuffer, Buffer.from([0x0d])]);
        //console.log(finalMessage.toString('hex'));

        return finalMessage;
    }

    // Function to parse command response
    // Type 1 (not readable): 292985002842ac8b29240824090128014513341210352300000336fb0037227fff1a00000000000000003ac50d
    // Type 2: 292984006d45afc407 5443503a6770732e696e6176636c6f75642e636f6d2c353333352c696e7465726e65743b49443a36393437363830373b475052533a363b5250543a33302c36303b48443a303b47534d3a313b5349473a32393b5341543a312c33313b545a3a2b303b4143433a31aa0d
    static parseCommandResponse = (message) => {

        const imeiHex = message.slice(5, 9);
        const imei = `${('0' + imeiHex[0].toString(10)).slice(-2)}${('0' + (imeiHex[1] - 0x80).toString(10)).slice(-2)}${('0' + (imeiHex[2] - 0x80).toString(10)).slice(-2)}${('0' + imeiHex[3].toString(10)).slice(-2)}`;

        const data = AmwellHandler.getAsciiSegment(message, 9, message.length - 2);
        //console.log(imei, data);

        const responseToCommand = data;

        return {
            imei,
            responseToCommand
        }
    }

    // Checksum calculation function
    static checksum = (data) => {
        let checksum = 0;
        for (let i = 0; i < data.length; i++) {
            checksum = checksum ^ data[i];
        }
        return checksum;
    }

}
module.exports = AmwellHandler;
