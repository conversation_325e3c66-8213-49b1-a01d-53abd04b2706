<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iNav Vehicle Replay</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #F4F8FA;
            color: #061238;
            text-align: center;
        }

        .content {
            height: 40px;
            padding: 5px;
            align-items: center;
            justify-content: space-between;
        }

        .content h1 {
            color: #0A4951;
            font-size: 15px;
            padding: 10px;
        }

        .row {
            display: flex;
            height: calc(100% - 60px);
            border-top: 1px solid #DAE6EF;
        }

        .map-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100%;
            transition: width 0.2s;
        }

        /* Mobile layout */
        @media (max-width: 768px) {
            .sidebar-container {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            #imeiLabel,
            .stop-list {
                width: 100%;
                text-align: center;
            }

            .map-container {
                width: 100%;
            }

            .toggle-button {
                display: block;
            }
        }

        #map {
            height: 100%;
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex-grow: 1;
        }

        .material-icons-outlined {
            vertical-align: middle;
        }

        .toggle-button {
            display: block;
            position: absolute;
            top: 5px;
            right: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .toggle-button:hover {
            background-color: #DAE6EF;
        }

        .leaflet-marker-icon.start {
            background-color: #3BB3C3;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            width: 50px;
            height: 50px;
        }

        .leaflet-marker-icon.end {
            background-color: #156E7F;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            width: 50px;
            height: 50px;
        }

        #controls {
            display: flex;
            width: 70%;
            align-items: center;
            gap: 10px;
            position: absolute;
            bottom: 30px;
            left: calc(50% - 35%);
            z-index: 1010;
            background: #F9FBFD;
            padding: 10px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #playButton {
            background-color: #3BB3C3;
            color: white;
            padding: 5px 20px;
            font-size: 13px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        #playButton:hover {
            background-color: #156E7F;
        }

        #playButton.playing {
            background-color: red;
        }

        #playSlider {
            -webkit-appearance: none;
            width: 100%;
            height: 6px;
            background: #ddd;
            outline: none;
            border-radius: 5px;
            transition: background 0.3s ease;
            cursor: pointer;
        }

        #playSlider:hover {
            background: #ccc;
        }

        #playSlider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            background: #3BB3C3;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.3s ease, transform 0.2s ease;
        }

        #playSlider::-webkit-slider-thumb:hover {
            background: #156E7F;
            transform: scale(1.1);
        }

        #playSlider::-moz-range-thumb {
            width: 15px;
            height: 15px;
            background: #3BB3C3;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.3s ease, transform 0.2s ease;
        }

        .date-range {
            display: inline-block;
            text-align: center;
            color: #156E7F;
            font-size: 11px;
            padding: 5px;
            border-radius: 8px;
            background: white;
            border: 1px solid #DAE6EF;
            width: 300px;
        }

        .flatpickr-calendar {
            font-size: 11px;
        }

        .flatpickr-months {
            font-size: 11px
        }

        .car-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 12px;
            text-align: center;
            color: #000;
            cursor: pointer;
            z-index: 1020;
        }

        .rotate-icon {
            position: absolute;
            width: 100%;
            height: 100%;
            bottom: 0px;
            left: 0px;
            transform-origin: center center;
        }

        .arrow-icon {
            pointer-events: none;
            position: absolute;
            font-size: 9px;
            z-index: 1000;
        }

        .arrow-icon>div {
            transform-origin: center center;
            appearance: none;
            -webkit-appearance: none;
            display: block;
            pointer-events: none;
        }

        .visit-number-inner {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: orange;
            color: white;
            text-align: center;
            line-height: 17px;
            font-weight: bold;
            border: 2px solid darkorange;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            font-size: 10px;
            z-index: 1010;
        }

        /* Sidebar container */
        .sidebar-container {
            background-color: #f4f6f9;
            width: 300px;
            max-height: 100vh;
            overflow-y: auto;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-family: 'Arial', sans-serif;
            color: #333;
        }

        #imeiLabel {
            position: relative;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
        }

        /* Stop List Styling */
        .stop-list {
            padding: 0;
            margin: 0;
            list-style-type: none;
        }

        /* Individual Stop Item */
        .stop-list li {
            background-color: #fff;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        /* Stop Item Hover Effect */
        .stop-list li:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* Stop Title */
        .stop-list li strong {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        /* Stop Text */
        .stop-list li p {
            margin: 0;
            line-height: 1.0;
            color: #666;
            font-size: 12px;
        }

        /* Timestamp Styling */
        .stop-list li .timestamp {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }

        /* Ignition status Styling */
        .stop-list li .ignition-status {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* Duration Badge */
        .stop-list li .duration-badge {
            display: inline-block;
            padding: 5px 10px;
            font-size: 11px;
            background-color: #ffcc00;
            color: #333;
            border-radius: 20px;
            margin-top: 5px;
            text-align: center;
        }

        .spinner {
            border: 4px solid #F9FBFD;
            border-top: 4px solid #3BB3C3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translate(-50%, -50%);
            z-index: 1100;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .chart-container {
            display: none;
            position: absolute;
            bottom: 80px;
            left: 10px;
            width: 400px;
            /* Adjust as needed */
            height: 200px;
            background-color: white;
            border: 1px solid #DAE6EF;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            cursor: grab;
            /* Show grabbing cursor */
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mark.js/8.11.1/mark.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Chart.js 4.4.0 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/hammerjs@latest"></script>

</head>

<body>
    <div class="content">
        <h1>iNav Vehicle Replay</h1>
        <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">
            <i class="material-icons-outlined">view_sidebar</i>
        </button>
    </div>

    <div class="row">
        <div class="map-container">
            <div id="map" style="height: 100%; width: 100%;"></div>
            <div id="controls">
                <!-- Date Range Picker -->
                <input type="text" id="dateRangePicker" class="date-range" placeholder="Select Date Range">

                <!-- Play Button -->
                <button id="playButton">Play</button>

                <!-- Play Slider -->
                <input type="range" id="playSlider" min="0" max="100" value="0" step="1">
            </div>

            <!-- Spinner -->
            <div id="spinner" class="spinner" style="display: none;"></div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar-container" id="sidebar">
            <div id="imeiLabel">IMEI Label</div>
            <div id="stopList" class="stop-list">
                <div class="stop-item"></div>
            </div>
        </div>

        <!-- Fuel Level Chart -->
        <div id="fuelChartContainer" class="chart-container">
            <canvas id="fuelLevelChart"></canvas>
        </div>
    </div>

    <script>

        // Toggle the sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mapColumn = document.querySelector('.map-container');
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '50%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '50%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            } else {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '22.95%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '77.05%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            }
        }

        document.addEventListener("DOMContentLoaded", function () {
            const chartContainer = document.querySelector(".chart-container");

            let isDragging = false;
            let offsetX, offsetY;

            chartContainer.addEventListener("mousedown", function (e) {
                isDragging = true;
                offsetX = e.clientX - chartContainer.getBoundingClientRect().left;
                offsetY = e.clientY - chartContainer.getBoundingClientRect().top;
                chartContainer.style.cursor = "grabbing";
            });

            document.addEventListener("mousemove", function (e) {
                if (!isDragging) return;

                const newX = e.clientX - offsetX;
                const newY = e.clientY - offsetY;

                chartContainer.style.left = `${newX}px`;
                chartContainer.style.top = `${newY}px`;
            });

            document.addEventListener("mouseup", function () {
                isDragging = false;
                chartContainer.style.cursor = "grab";
            });
        });

        // Initialize the map
        document.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const imei = urlParams.get('imei');
            const port = urlParams.get('port');
            const name = urlParams.get('name');
            const calibration = urlParams.get('calibration');
            //const imei = '860896052073897';
            //const port = '3005';

            // update imei label
            document.getElementById('imeiLabel').textContent = `${name} - IMEI: ${imei}`;

            const map = L.map('map').setView([12, 122], 6);

            /*L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);*/

            const osm = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            });

            const googleSat = L.tileLayer('https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
                attribution: '&copy; Google Maps'
            });

            const baseMaps = {
                "OpenStreetMap": osm,
                "Google Satellite": googleSat
            };

            L.control.layers(baseMaps).addTo(map);

            // Add street view by right clicking on the map
            map.on('contextmenu', function (event) {
                let lat = event.latlng.lat;
                let lng = event.latlng.lng;
                window.open(`https://www.google.com/maps?q=&layer=c&cbll=${lat},${lng}&cbp=11,0,0,0,0`);
            });

            // Set default layer
            osm.addTo(map);

            let zoomAdjusted = false;
            let polylinePoints = [];
            let polyline, carMarker, startMarker, endMarker, arrows, stopMarkers;
            let slider = document.getElementById("playSlider");
            let playButton = document.getElementById("playButton");
            let sliderInputListener, playButtonClickListener, playButtonClickEventListener, playButtonTouchEndListener;
            let playing = false;
            let animationFrame;
            let animationIndex = 0;

            let options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true,
                timeZone: 'Asia/Manila'
            };

            // Detect zoom and adjust map
            map.on('zoom', (e) => {
                zoomAdjusted = true;
            });

            document.getElementById('toggleButton').addEventListener('click', () => {
                setTimeout(() => {
                    map.invalidateSize();
                }, 200);
            });

            // Initialize Date Range Picker with today's date as default
            const today = new Date();
            const formattedToday = `${today.toLocaleString('default', { month: 'short' })}-${today.getDate()}`; // M-d format

            flatpickr("#dateRangePicker", {
                mode: "range",
                dateFormat: "M-d H:i", // Include time in the format
                enableTime: true, // Enable time selection
                time_24hr: true, // Use 24-hour format
                defaultHour: 0, // Default start and end hours
                defaultMinute: 0, // Default start and end minutes
                defaultDate: [
                    new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0), // Start of today
                    new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59) // End of today
                ],

                onChange: function (selectedDates, dateStr, instance) {
                    if (selectedDates.length == 2 && (selectedDates[1].getHours() === 0 || selectedDates[1].getHours() === 23)) {
                        selectedDates[1].setHours(23, 59, 0, 0);
                        instance.setDate(selectedDates, false); // Force flatpickr to update
                    }
                    console.log(selectedDates);
                },

                onClose: function (selectedDates, dateStr, instance) {
                    if (selectedDates.length === 2) {
                        startUTC = selectedDates[0].toISOString();
                        endUTC = selectedDates[1].toISOString();
                        plotTrackData(imei, startUTC, endUTC);
                    }
                }
            });

            // Fetch track data for the given IMEI and date range
            async function fetchTrackData(imei, startDate, endDate) {
                const spinner = document.getElementById('spinner');
                spinner.style.display = 'block';

                const url = `http://dev.inavcloud.com:${port}/api/messages?imei=${imei}&startDate=${startDate}&endDate=${endDate}`;
                console.log(`Fetching url: ${url}`);
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    data.sort((a, b) => a.gpsTimestamp - b.gpsTimestamp);
                    return data;
                } catch (error) {
                    console.error(`Error fetching track data:`, error);
                    return null;
                } finally {
                    spinner.style.display = 'none'; // Hide spinner after fetching is complete
                }
            }

            // Plot track data on the map based on selected dates
            async function plotTrackData(imei, startDate, endDate) {
                // Clear existing items before plotting new data
                if (polyline) map.removeLayer(polyline);
                if (carMarker) map.removeLayer(carMarker);
                if (startMarker) map.removeLayer(startMarker);
                if (endMarker) map.removeLayer(endMarker);

                const data = await fetchTrackData(imei, startDate, endDate);
                let distAccumulated = 0;

                if (data && data.length > 0) {
                    polylinePoints = data.map(({ latitude, longitude }) => [parseFloat(latitude), parseFloat(longitude)]);

                    polyline = L.polyline(polylinePoints, { color: '#669DF6', weight: 5 }).addTo(map);

                    // Add start end label
                    startIcon = L.divIcon({
                        className: 'start',
                        html: `<div>Start</div>`,
                    });
                    endIcon = L.divIcon({
                        className: 'end',
                        html: `<div>End</div>`,
                    });
                    startMarker = L.marker(polylinePoints[0], { icon: startIcon }).addTo(map).bindPopup("");
                    endMarker = L.marker(polylinePoints[polylinePoints.length - 1], { icon: endIcon }).addTo(map).bindPopup("");

                    // get accCode from data and set icon
                    const { accCode } = data[0];
                    const iconUrl = accCode === '1'
                        ? icon_running
                        : icon_ignition_off;

                    const iconHtml = `
                        <div class="car-icon">
                        <div class="rotate-icon" style="transform: rotate(-90deg);">
                        <img src="${iconUrl}" />
                        </div>
                    `;

                    const carIcon = L.divIcon({
                        className: '',
                        html: iconHtml,
                        iconAnchor: [16, 16],
                        popupAnchor: [0, -16],
                    });

                    carMarker = L.marker(polylinePoints[0], { icon: carIcon }).addTo(map).bindPopup("");

                    // Fit the map to polyline bounds
                    map.fitBounds(polyline.getBounds());

                    // Calculate accumulated distance and add it to each data point
                    for (let i = 1; i < data.length; i++) {
                        const prevPoint = data[i - 1];
                        const currPoint = data[i];
                        const distance = Math.sqrt(Math.pow(prevPoint.latitude - currPoint.latitude, 2) + Math.pow(prevPoint.longitude - currPoint.longitude, 2)) * 111 * 1000;
                        distAccumulated += distance / 1000;
                        data[i].distAccumulated = distAccumulated;
                        i === 1 ? data[0].distAccumulated = 0 : null;
                    }

                    initializeSliderAndPlayButton(data);

                    map.on('moveend', () => {
                        // Add arrows along the polyline after map.fitBounds
                        if (arrows) arrows.forEach(arrow => map.removeLayer(arrow));

                        const arrowColor = '#156E7F';
                        const arrowDistance = 200;
                        arrows = getArrows(polylinePoints, arrowColor, arrowDistance, map);
                        arrows.forEach(arrow => arrow.addTo(map));
                    });

                    plotStopLocations(data, map);
                    createChart(data);
                }
            }

            // Initial plot with today's date
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0).toISOString();
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString();
            plotTrackData(imei, startOfDay, endOfDay);

            function initializeSliderAndPlayButton(data) {
                if (sliderInputListener) slider.removeEventListener('input', sliderInputListener);
                if (playButtonClickListener) {
                    playButton.removeEventListener('click', playButtonClickEventListener);
                    playButton.removeEventListener('touchend', playButtonTouchEndListener);
                }

                slider.value = 0;
                animationIndex = 0;
                playing = false;
                playButton.classList.remove('playing');
                playButton.textContent = "Play";

                slider.max = data.length - 1;

                // Define new event listeners
                sliderInputListener = function () {
                    const newIndex = parseInt(slider.value, 10);
                    animationIndex = newIndex;
                    moveCarToPoint(animationIndex, data);

                    // Pause the animation if playing
                    if (playing) {
                        cancelAnimationFrame(animationFrame);  // Stop current animation
                        playing = false;
                        playButton.classList.remove('playing');
                        playButton.textContent = "Play";
                    }
                };

                playButtonClickListener = function () {
                    if (animationIndex >= polylinePoints.length) {
                        // Reset to the beginning if the animation has finished
                        animationIndex = 0;
                        slider.value = 0;
                        moveCarToPoint(animationIndex, data);
                    }

                    playing = !playing;
                    if (playing) {
                        playButton.classList.add('playing');
                        playButton.textContent = "Pause";
                        animateCar(data);  // Resume animation from the current index
                    } else {
                        playButton.classList.remove('playing');
                        playButton.textContent = "Play";
                        cancelAnimationFrame(animationFrame);
                    }
                };

                // Store references to the actual event listener functions
                playButtonClickEventListener = function (e) {
                    e.preventDefault();
                    playButtonClickListener();
                };

                playButtonTouchEndListener = function (e) {
                    e.preventDefault();
                    playButtonClickListener();
                };

                // Add new event listeners
                slider.addEventListener('input', sliderInputListener);
                playButton.addEventListener('click', playButtonClickEventListener);
                playButton.addEventListener('touchend', playButtonTouchEndListener);
            }

            function moveCarToPoint(index, data) {
                const latLng = index === 0 ? [0, 0] : [polylinePoints[index - 1][0], polylinePoints[index - 1][1]];

                if (index > 0 && index < polylinePoints.length) {
                    carMarker.setLatLng(polylinePoints[index]);

                    const { gpsTimestamp, latitude, longitude, speed, course, satelliteCount, sosCode, accCode, doorCode, temperature, mccMnc, gsm, extBat, intBat, fuelLevel, distAccumulated } = data[index];

                    const dist = index === 0 ? 0 : Math.sqrt(Math.pow(latLng[0] - latitude, 2) + Math.pow(latLng[1] - longitude, 2)) * 111 * 1000;
                    const gpsTimeDiff = (new Date(gpsTimestamp).getTime() - new Date(data[index - 1].gpsTimestamp).getTime()) / 1000;
                    const gpsSpeed = dist / gpsTimeDiff * 3.6;


                    // Formatting and data processing
                    let timestamp = new Date(gpsTimestamp.replace(" ", "T") + "Z");
                    const formattedDate = timestamp.toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');

                    //  - Dist: ${dist.toFixed(2)} m - Time Diff: ${gpsTimeDiff} s - GPS Speed: ${gpsSpeed.toFixed(0)} km/h
                    carMarker.getPopup().setContent(`
                        <b>${name}</b><br>
                        IMEI: ${imei}<br>
                        Time: ${formattedDate}<br>
                        Lat/Lon: ${latitude}, ${longitude} - Mileage: ${distAccumulated.toFixed(0)} km<br>
                        Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                        ${fuelLevel ? ` - Fuel: ${fuelLevel}` : ``}
                        ${temperature ? ` - Temp: ${temperature}` : ``}
                        ${doorCode ? ` - Door: ${doorCode}` : ``}
                        ${sosCode ? ` - SOS: ${sosCode}` : ``}
                        ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ``}
                        ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ``}
                    `);
                    carMarker.openPopup();

                    const iconUrl = accCode === '1'
                        ? icon_running
                        : icon_ignition_off;

                    const iconHtml = `
                        <div class="car-icon">
                        <div class="rotate-icon" style="transform: rotate(${course - 90}deg);">
                        <img src="${iconUrl}" />
                        </div>
                        </div>
                    `;

                    // Update the car marker's icon
                    carMarker.setIcon(L.divIcon({
                        className: '',
                        html: iconHtml,
                        iconAnchor: [16, 16],
                        popupAnchor: [0, -16],
                    }));
                }
            }

            function animateCar(data) {
                if (!playing) return; // Stop the animation if not playing

                if (animationIndex < polylinePoints.length) {
                    moveCarToPoint(animationIndex, data);
                    slider.value = animationIndex;
                    animationIndex++;
                    animationFrame = requestAnimationFrame(() => animateCar(data));
                } else {
                    // Reset animation
                    animationIndex = 0;
                    playing = false;
                    playButton.classList.remove('playing');
                    playButton.textContent = "Play";
                }
            }


            let fuelChartInstance = null; // Ensure this is globally defined
            function createChart(data) {
                const chartContainer = document.getElementById('fuelChartContainer');
                const fuelLevelChart = document.getElementById('fuelLevelChart');

                if (!fuelLevelChart) {
                    console.error('Canvas element not found!');
                    return;
                }

                const ctx = fuelLevelChart.getContext('2d');

                // Filter valid fuel level data
                const validData = data.filter(item => item.fuelLevel !== null && !isNaN(item.fuelLevel));

                // Destroy existing chart if it exists
                if (fuelChartInstance) {
                    fuelChartInstance.destroy();
                }

                // Show or hide the chart based on data availability
                chartContainer.style.display = validData.length > 0 ? 'block' : 'none';

                const chartData = validData.map(data => {
                    const timestamp = new Date(data.gpsTimestamp.replace(" ", "T") + "Z");
                    const localTime = timestamp.toLocaleString('en-US', options); // Adjust options as needed
                    return {
                        x: new Date(localTime),
                        y: {
                            fuelLevel: Number(data.fuelLevel),
                            speed: Number(data.speed),
                            accCode: Number(data.accCode),
                        }
                    };
                });

                // Create new chart
                fuelChartInstance = new Chart(ctx, {
                    type: 'line',
                    data: {
                        datasets: [{
                            label: 'Fuel Level',
                            data: chartData.map(data => ({ x: data.x, y: data.y.fuelLevel })),
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1.5,
                            fill: false,
                            pointRadius: 1.5,
                            yAxisID: 'y-fuel',
                            tension: 0.3, // Smooth curve
                            pointHoverRadius: 5,
                            tooltips: {
                                enabled: true
                            }
                        }, {
                            label: 'Speed',
                            data: chartData.map(data => ({ x: data.x, y: data.y.speed })),
                            borderColor: 'rgba(255, 99, 132, 0.2)',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            fill: false,
                            pointRadius: 0,
                            yAxisID: 'y-speed',
                            spanGaps: true,
                            showLine: true,
                            tooltips: {
                                enabled: false
                            }
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'hour',
                                    displayFormats: {
                                        hour: 'HH:mm'
                                    },
                                    min: chartData[0].x,
                                    max: chartData[chartData.length - 1].x
                                },
                                ticks: {
                                    font: {
                                        size: 10 // Adjust font size (try 8, 10, or 12 based on your need)
                                    },
                                    maxRotation: 0, // Prevents label rotation
                                    autoSkip: true // Automatically skips labels to prevent overlap
                                },
                                title: {
                                    display: true,
                                    text: 'Time',
                                    font: {
                                        size: 12 // Adjust axis title font size
                                    }
                                }
                            },
                            'y-fuel': {
                                type: 'linear',
                                position: 'left',
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: 'Fuel Level'
                                }
                            },
                            'y-speed': {
                                type: 'linear',
                                position: 'right',
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: 'Speed'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            },
                        }
                    }
                });
            }

            const filterStopDuration = 3; // in minutes
            const filterStopDistance = 100; // in meters
            async function plotStopLocations(data, map) {
                if (stopMarkers) stopMarkers.forEach(marker => map.removeLayer(marker));

                stopMarkers = [];
                const stopLocations = [];
                let stopLocation = null;
                let lastLocation = null;
                let lastAccCode = null;
                let lastDoorCode = null; // Add variable to store doorCode
                let stopStartTime = null;
                let lastGpsTimestamp = null;
                let lastGpsLocation = null;
                let speedInt = 0;
                let reducedFilterStopDuration = filterStopDuration;

                for (let i = 0; i < data.length; i++) {
                    const { gpsTimestamp, latitude, longitude, speed, course, accCode, doorCode } = data[i];
                    const gpsTimeDiff = lastGpsTimestamp ? (new Date(gpsTimestamp).getTime() - new Date(lastGpsTimestamp).getTime()) / 1000 : 0;
                    const gpsDistance = lastGpsLocation ? Math.sqrt(Math.pow(lastGpsLocation.latitude - latitude, 2) + Math.pow(lastGpsLocation.longitude - longitude, 2)) * 111 * 1000 : 0;
                    const gpsSpeed = gpsTimeDiff > 0 ? gpsDistance / gpsTimeDiff * 3.6 : 0;
                    speedInt = parseInt(speed);

                    if (gpsTimeDiff >= filterStopDuration * 60) {
                        speedInt = gpsSpeed;
                        reducedFilterStopDuration = filterStopDuration; // * 0.5;
                    }

                    const dist = stopLocation ? Math.sqrt(Math.pow(stopLocation.latitude - latitude, 2) + Math.pow(stopLocation.longitude - longitude, 2)) * 111 * 1000 : 0;

                    if (stopLocation && (speedInt > 5 && dist > filterStopDistance)) {
                        const stopDuration = (new Date(lastGpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000; // in minutes
                        if (stopDuration >= reducedFilterStopDuration) {
                            stopLocations.push({
                                ...stopLocation,
                                stopStartTime,
                                stopEndTime: lastGpsTimestamp,
                                stopDuration,
                                lastAccCode,
                                lastDoorCode
                            });
                            reducedFilterStopDuration = filterStopDuration;
                            console.log('Included stop end (g/gtd/s/a/d/sd):', gpsTimestamp, gpsTimeDiff, speedInt, accCode, dist, stopDuration);
                        } else {
                            console.log('Excluded stop end (g/gtd/s/a/d/sd):', gpsTimestamp, gpsTimeDiff, speedInt, accCode, dist, stopDuration);
                        }
                        stopLocation = null;
                        stopStartTime = null;
                    }

                    if (!stopLocation && (speedInt <= 5 || (speedInt <= 5 && accCode === '0'))) {
                        // New stop detected
                        stopLocation = { latitude, longitude };
                        stopStartTime = gpsTimestamp;
                        lastAccCode = accCode;
                        lastDoorCode = doorCode; // Store doorCode value

                        console.log('New stop (g/gtd/s/a/d):', gpsTimestamp, gpsTimeDiff, speedInt, accCode, doorCode)
                    }

                    lastGpsTimestamp = gpsTimestamp;
                    lastGpsLocation = { latitude, longitude };
                }

                // Handle the last stop if the device is still stopped at the end of the data
                if (stopLocation) {
                    const stopDuration = (new Date(data[data.length - 1].gpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000;
                    stopLocations.push({
                        ...stopLocation,
                        stopStartTime,
                        stopEndTime: data[data.length - 1].gpsTimestamp,
                        stopDuration,
                        lastAccCode,
                        lastDoorCode
                    });
                }

                console.log('stopLocations:', stopLocations);

                // Plot markers with visit number labels on the map and bind popups
                stopLocations.forEach((location, index) => {
                    const { latitude, longitude, stopStartTime, stopEndTime, stopDuration, lastAccCode, lastDoorCode } = location;

                    // Create marker with encircled visit number
                    const stopMarker = L.marker([parseFloat(latitude), parseFloat(longitude)], {
                        icon: L.divIcon({
                            className: 'visit-number',
                            html: `<div class="visit-number-inner">${index + 1}</div>`,
                            iconSize: [20, 20],
                            iconAnchor: [10, 0],
                        }),
                    });

                    // Add the marker to the map
                    stopMarker.addTo(map);
                    stopMarkers.push(stopMarker);

                    // Format stopStartTime, stopEndTime and stopDuration
                    const stopStartTimeFormatted = new Date(stopStartTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                    const stopEndTimeFormatted = new Date(stopEndTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                    const hours = Math.floor(stopDuration / 60);
                    const minutes = Math.round(stopDuration % 60);
                    const stopDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${Math.round(stopDuration)} min`;

                    // Bind popup with stopStartTime and stopDuration
                    stopMarker.bindPopup(`
                        <b>Stop ${index + 1}:</b><br>
                        <b>Stop Time:</b> ${stopStartTimeFormatted}<br>
                        <b>End Time:</b> ${stopEndTimeFormatted}<br>
                        <b>Duration:</b> ${stopDurationFormatted}<br>
                        Acc: ${lastAccCode} ${lastDoorCode ? `Door: ${lastDoorCode}` : ''}
                        `);

                    stopMarker.on('click', function (e) {
                        stopMarker.openPopup();
                    });
                });

                // Update the sidebar with the list of stop locations
                updateSidebarWithStops(stopLocations);
            }

            function updateSidebarWithStops(stopLocations) {
                const sidebar = document.getElementById('stopList'); // Correct element ID
                sidebar.innerHTML = ""; // Clear existing content

                if (stopLocations.length > 0) {
                    const stopList = document.createElement('ul'); // Create a list element
                    stopList.classList.add('stop-list'); // Add the stop-list class

                    stopLocations.forEach(async (location, index) => {
                        const stopItem = document.createElement('li');
                        stopItem.classList.add('stop-item'); // Add the stop-item class

                        const startTimestamp = new Date(location.stopStartTime.replace(" ", "T") + "Z")
                            .toLocaleString('en-US', options)
                            .replace(/\//g, '-')
                            .replace(',', '');

                        const endTimestamp = new Date(location.stopEndTime.replace(" ", "T") + "Z")
                            .toLocaleString('en-US', options)
                            .replace(/\//g, '-')
                            .replace(',', '');

                        const hours = Math.floor(location.stopDuration / 60);
                        const minutes = Math.round(location.stopDuration % 60);
                        const stopDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${minutes} min`;

                        stopItem.innerHTML = `
                            <strong>Stop ${index + 1}:</strong>
                            <p class="timestamp">${startTimestamp}<br>
                            ${endTimestamp}</p>
                            <p>Address: Fetching address...</p> <!-- Placeholder for the address -->
                            <p class="ignition-status">Acc: ${location.lastAccCode} ${location.lastDoorCode ? `Door: ${location.lastDoorCode}` : ''}</p>
                            <span class="duration-badge">Duration: ${stopDurationFormatted}</span>
                        `;
                        stopList.appendChild(stopItem); // Append stop item to the list

                        // Fetch the address and update the innerHTML once it's available
                        const address = await getAddress(location.latitude, location.longitude);
                        stopItem.querySelector('p:nth-child(3)').innerHTML = `<p>Address: ${address || 'Not available'}</p>`;

                        // Add event listener to zoom to the stop location when clicked
                        stopItem.addEventListener('click', () => {
                            map.setView([parseFloat(location.latitude), parseFloat(location.longitude)], 16); // Zoom to the stop location
                        });
                    });

                    sidebar.appendChild(stopList); // Append the stop list to the sidebar
                } else {
                    sidebar.innerHTML = "<p>No stops during this period.</p>";
                }
            }

            async function getAddress(lat, lng) {
                const apiKey = '031b3fe673e747deab496415d4332359'; // Replace with your OpenCage API key
                const url = `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lng}&key=${apiKey}`;

                try {
                    const response = await fetch(url);
                    const data = await response.json();

                    if (data.results && data.results.length > 0) {
                        const address = data.results[0].formatted;
                        return address;
                    } else {
                        console.log('No results found');
                    }
                } catch (error) {
                    console.error('Error:', error);
                }
            }


            // https://stackoverflow.com/questions/53307322/leaflet-polyline-arrows
            function getArrows(arrLatLngs, color = '', arrowDistance = 1.5, mapObj) {

                if (!Array.isArray(arrLatLngs) || arrLatLngs.length < 2) return [];

                const result = [];
                const arrowStyle = color ? `color:${color}` : '';
                let lastArrowDistance = 0;

                for (let i = 1; i < arrLatLngs.length; i++) {

                    const segmentDistance = distanceTo(mapObj.project(new L.latLng(arrLatLngs[i - 1])), mapObj.project(new L.latLng(arrLatLngs[i])));

                    lastArrowDistance = lastArrowDistance + segmentDistance;

                    // Skip if the segment is too short
                    if (lastArrowDistance < arrowDistance) continue;

                    // Calculate the number of arrows for this segment
                    const numArrows = Math.floor(lastArrowDistance / arrowDistance);

                    const angle = getAngle(arrLatLngs[i - 1], arrLatLngs[i], -1);
                    const icon = L.divIcon({ className: 'arrow-icon', bgPos: [5, 5], html: `<div style="${arrowStyle}; transform: rotate(${angle}deg)">▶</div>` });

                    // Place arrows at regular intervals along the segment
                    for (let j = 1; j <= numArrows; j++) {
                        const fraction = (lastArrowDistance - arrowDistance * j) / segmentDistance;
                        const midPoint = myMidPoint(arrLatLngs[i], arrLatLngs[i - 1], fraction, mapObj);
                        result.push(L.marker(midPoint, { icon }));
                    }
                    lastArrowDistance = 0;
                }
                return result;
            }

            function getAngle(latLng1, latLng2, coef) {
                const radToDeg = 180 / Math.PI;
                const dy = latLng2[0] - latLng1[0];
                const dx = Math.cos(latLng1[0] * Math.PI / 180) * (latLng2[1] - latLng1[1]);
                const angle = Math.atan2(dy, dx) * radToDeg * coef;
                return angle.toFixed(2);
            }

            function myMidPoint(latLng1, latLng2, per, mapObj) {
                const p1 = mapObj.project(new L.latLng(latLng1));
                const p2 = mapObj.project(new L.latLng(latLng2));

                const dist = distanceTo(p1, p2);
                const halfDist = dist * per;

                if (halfDist === 0) return mapObj.unproject(p1);

                const ratio = (dist - halfDist) / dist;
                const midPoint = new Point(p2.x - ratio * (p2.x - p1.x), p2.y - ratio * (p2.y - p1.y));
                const res = mapObj.unproject(midPoint);

                return [res.lat, res.lng];
            }

            function distanceTo(p1, p2) {
                const x = p2.x - p1.x;
                const y = p2.y - p1.y;
                return Math.sqrt(x * x + y * y);
            }

            function Point(x, y, round = false) {
                this.x = round ? Math.round(x) : x;
                this.y = round ? Math.round(y) : y;
            }

        });

        // icons
        const icon_running = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#ff6114" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');

        const icon_ignition_off = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#40e0d0" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');

    </script>

</body>

</html>