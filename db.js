// npm install sqlite3
// npm install node-cron

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const cron = require('node-cron');

const DB_NAME = 'mpuvgps.db';
const dbPath = path.resolve(__dirname, 'db', DB_NAME);
const setDays = 31;

let dbInstance = null;
const imeiLastRecords = new Map();

function getDbInstance() {
    if (!dbInstance) {
        dbInstance = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
            if (err) {
                console.error('Error opening database:', err.message);
            } else {
                console.log(`Connected to the SQLite database at ${dbPath}.`);

                // Optimize database performance with PRAGMA settings
                dbInstance.serialize(() => {
                    const pragmas = [
                        'PRAGMA journal_mode = WAL',         // Write-Ahead Logging
                        'PRAGMA synchronous = NORMAL',     // Increased durability
                        'PRAGMA cache_size = -100000',       // 100 MB cache
                        'PRAGMA locking_mode = EXCLUSIVE', // NORMAL or EXCLUSIVE Less conflict between read and write
                        'PRAGMA temp_store = MEMORY',       // Temp tables in memory
                        'PRAGMA busy_timeout = 10000',       // Wait for 10 seconds
                        'PRAGMA foreign_keys = ON'           // If you use foreign key relationship
                    ];

                    pragmas.forEach((pragma) => {
                        dbInstance.run(pragma, (err) => {
                            if (err) console.error('Error setting PRAGMA:', err.message);
                        });
                    });
                });
            }
        });
    }
    return dbInstance;
}

function createTableForIMEI(db, imei) {
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, ''); // Sanitize IMEI to avoid injection
    const tableName = `gps_data_${sanitizedImei}`;

    db.serialize(() => {
        db.run(`CREATE TABLE IF NOT EXISTS ${tableName} (
                serverTimestamp TEXT,
                gpsTimestamp TEXT,
                latitude TEXT,
                longitude TEXT,
                speed TEXT,
                course TEXT,
                satelliteCount TEXT,
                accCode TEXT
            )`, (err) => {
            if (err) {
                console.error(`Error creating table for IMEI: ${sanitizedImei}`, err.message);
            } else {
                db.run(`CREATE INDEX IF NOT EXISTS idx_${sanitizedImei}_gpsTimestamp ON ${tableName} (gpsTimestamp)`);
            }
        });
    });
}

function getArchiveDbInstance(month) {
    const ARCHIVE_DB_NAME = `mpuvgps_archive_${month}.db`;
    const archiveDbPath = path.resolve(__dirname, 'db', ARCHIVE_DB_NAME);

    let archiveDbInstance = new sqlite3.Database(archiveDbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
        if (err) {
            console.error(`Error opening archive database for ${month}`, err.message);
        } else {
            console.log(`Connected to the SQLite ${ARCHIVE_DB_NAME} archive database.`);
        }
    });

    // Apply PRAGMA settings to prevent database locks
    archiveDbInstance.run(`PRAGMA journal_mode = WAL`);
    archiveDbInstance.run(`PRAGMA synchronous = NORMAL`);
    archiveDbInstance.run(`PRAGMA busy_timeout = 30000`); // Wait up to 30 seconds for locks to clear
    archiveDbInstance.run(`PRAGMA temp_store = MEMORY`);
    archiveDbInstance.run(`PRAGMA locking_mode = NORMAL`);

    return archiveDbInstance;
}

function getAllImeis(db, callback) {
    db.all("SELECT DISTINCT name FROM sqlite_master WHERE type='table' AND name LIKE 'gps_data_%'", (err, rows) => {
        if (err) {
            console.error('Error retrieving IMEIs', err.message);
            callback(err, null);
        } else {
            const imeis = rows.map(row => row.name.split('_')[2]);
            callback(null, imeis);
        }
    });
}

function createArchiveTable(db, imei) {
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');
    const tableName = `gps_data_${sanitizedImei}`;

    // Check if db has serialize function
    if (typeof db.serialize === 'function') {
        db.serialize(() => {
            db.run(`CREATE TABLE IF NOT EXISTS ${tableName} (
                    serverTimestamp TEXT,
                    gpsTimestamp TEXT,
                    latitude TEXT,
                    longitude TEXT,
                    speed TEXT,
                    course TEXT,
                    satelliteCount TEXT,
                    accCode TEXT
                )`, (err) => {
                if (err) {
                    console.error(`Error creating archive table for IMEI: ${sanitizedImei}`, err.message);
                }
            });
        });
    } else {
        // If serialize is not available, just run the query directly
        db.run(`CREATE TABLE IF NOT EXISTS ${tableName} (
                serverTimestamp TEXT,
                gpsTimestamp TEXT,
                latitude TEXT,
                longitude TEXT,
                speed TEXT,
                course TEXT,
                satelliteCount TEXT,
                accCode TEXT
            )`, (err) => {
            if (err) {
                console.error(`Error creating archive table for IMEI: ${sanitizedImei}`, err.message);
            }
        });
    }
}

// Archive records older than set days
function archiveOldRecords(mainDb, imei) {
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');
    const mainTableName = `gps_data_${sanitizedImei}`;
    const BATCH_SIZE = 100; // Process in smaller batches to reduce memory usage

    // Calculate the date for filtering records
    const setDaysAgo = new Date();
    setDaysAgo.setDate(setDaysAgo.getDate() - setDays);
    const setDaysAgoIso = setDaysAgo.toISOString().slice(0, 19).replace('T', ' '); // Ensure proper SQLite timestamp format

    // First, count the total records to archive
    const countQuery = `SELECT COUNT(*) as count FROM ${mainTableName} WHERE gpsTimestamp < ?`;

    mainDb.get(countQuery, [setDaysAgoIso], (countErr, countResult) => {
        if (countErr) {
            console.error(`Error counting records for IMEI: ${sanitizedImei}`, countErr.message);
            return;
        }

        const totalRecords = countResult.count;

        if (totalRecords === 0) {
            console.log(`No records to archive for IMEI: ${sanitizedImei}`);
            return;
        }

        console.log(`Found ${totalRecords} records to archive for IMEI: ${sanitizedImei}`);

        // Create a map to store archive database connections by month
        const archiveDbs = {};

        // Process records in batches
        let processedCount = 0;

        function processBatch(offset) {
            if (offset >= totalRecords) {
                console.log(`Finished processing all ${processedCount} records for IMEI: ${sanitizedImei}`);

                // Close all archive database connections
                Object.keys(archiveDbs).forEach(month => {
                    archiveDbs[month].close((closeErr) => {
                        if (closeErr) {
                            console.error(`Error closing archive database for ${month}:`, closeErr.message);
                        } else {
                            console.log(`Closed archive database for ${month}`);
                        }
                    });
                });

                // Delete archived records
                const deleteQuery = `DELETE FROM ${mainTableName} WHERE gpsTimestamp < ?`;
                mainDb.run(deleteQuery, [setDaysAgoIso], (deleteErr) => {
                    if (deleteErr) {
                        console.error(`Error deleting old records for IMEI: ${sanitizedImei}`, deleteErr.message);
                    } else {
                        console.log(`Deleted ${totalRecords} records older than ${setDaysAgoIso} for IMEI: ${sanitizedImei}`);
                    }
                });

                return;
            }

            // Select a batch of records
            const batchSize = Math.min(BATCH_SIZE, totalRecords - offset);
            const selectQuery = `SELECT * FROM ${mainTableName} WHERE gpsTimestamp < ? ORDER BY gpsTimestamp LIMIT ? OFFSET ?`;

            mainDb.all(selectQuery, [setDaysAgoIso, batchSize, offset], (err, rows) => {
                if (err) {
                    console.error(`Error selecting records batch for IMEI: ${sanitizedImei}`, err.message);
                    // Continue with next batch despite error
                    setTimeout(() => processBatch(offset + batchSize), 1000);
                    return;
                }

                if (rows.length === 0) {
                    // No more records, move to cleanup
                    processBatch(totalRecords);
                    return;
                }

                console.log(`Processing batch ${offset}-${offset + rows.length} of ${totalRecords} for IMEI: ${sanitizedImei}`);

                // Group records by month to minimize database operations
                const recordsByMonth = {};

                rows.forEach(row => {
                    const gpsTimestamp = new Date(row.gpsTimestamp);
                    const archiveMonth = gpsTimestamp.toISOString().slice(0, 7); // Format: YYYY-MM

                    if (!recordsByMonth[archiveMonth]) {
                        recordsByMonth[archiveMonth] = [];
                    }

                    recordsByMonth[archiveMonth].push(row);
                });

                // Process each month's records in a transaction
                const monthPromises = Object.keys(recordsByMonth).map(month => {
                    return new Promise((resolveMonth) => {
                        const monthRecords = recordsByMonth[month];

                        // Get or create archive database connection
                        if (!archiveDbs[month]) {
                            archiveDbs[month] = getArchiveDbInstance(month);
                            createArchiveTable(archiveDbs[month], imei);
                        }

                        const archiveDb = archiveDbs[month];
                        const archiveTableName = `gps_data_${sanitizedImei}`;

                        // Use a transaction for better performance
                        function runTransaction() {
                            archiveDb.run('BEGIN TRANSACTION', (beginErr) => {
                                if (beginErr) {
                                    console.error(`Error beginning transaction for month ${month}:`, beginErr.message);
                                    resolveMonth();
                                    return;
                                }

                                const insertQuery = `
                                    INSERT INTO ${archiveTableName}
                                    (serverTimestamp, gpsTimestamp, latitude, longitude, speed, course, satelliteCount, accCode)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                `;

                                const stmt = archiveDb.prepare(insertQuery);
                                let insertCount = 0;

                                // Insert records one by one
                                function insertNext(index) {
                                    if (index >= monthRecords.length) {
                                        // All records inserted, commit transaction
                                        stmt.finalize();

                                        archiveDb.run('COMMIT', (commitErr) => {
                                            if (commitErr) {
                                                console.error(`Error committing transaction for month ${month}:`, commitErr.message);
                                                // Try to rollback
                                                archiveDb.run('ROLLBACK');
                                            } else {
                                                processedCount += insertCount;
                                                console.log(`Archived ${insertCount} records for month ${month} for IMEI: ${sanitizedImei}`);
                                            }
                                            resolveMonth();
                                        });
                                        return;
                                    }

                                    const row = monthRecords[index];

                                    stmt.run([
                                        row.serverTimestamp, row.gpsTimestamp, row.latitude, row.longitude,
                                        row.speed, row.course, row.satelliteCount, row.accCode
                                    ], (insertErr) => {
                                        if (insertErr) {
                                            console.error(`Error inserting record into archive for IMEI: ${sanitizedImei}`, insertErr.message);
                                        } else {
                                            insertCount++;
                                        }

                                        // Process next record with a small delay to prevent blocking
                                        setImmediate(() => insertNext(index + 1));
                                    });
                                }

                                // Start inserting records
                                insertNext(0);
                            });
                        }

                        // Check if serialize is available
                        if (typeof archiveDb.serialize === 'function') {
                            archiveDb.serialize(runTransaction);
                        } else {
                            runTransaction();
                        }
                    });
                });

                // Wait for all month transactions to complete
                Promise.all(monthPromises).then(() => {
                    // Process next batch with a delay to prevent memory buildup
                    setTimeout(() => processBatch(offset + batchSize), 2000);
                }).catch(error => {
                    console.error(`Error processing batch for IMEI: ${sanitizedImei}`, error);
                    // Continue with next batch despite error
                    setTimeout(() => processBatch(offset + batchSize), 2000);
                });
            });
        }

        // Start processing the first batch
        processBatch(0);
    });
}

// Archive old records for all IMEIs
async function archiveOldRecordsForAllImeis() {
    const db = getDbInstance();

    return new Promise((resolve, reject) => {
        // Track if this function is already running
        if (global.isMpuvArchivingInProgress) {
            console.log('MPUV archiving process already running, skipping this run');
            resolve();
            return;
        }

        global.isMpuvArchivingInProgress = true;

        // Get memory usage before starting
        const memBefore = process.memoryUsage();
        console.log(`Memory usage before archiving: ${Math.round(memBefore.rss / 1024 / 1024)}MB RSS`);

        // Process IMEIs one by one to avoid memory issues
        function processImeis() {
            getAllImeis(db, (err, imeis) => {
                if (err) {
                    console.error('Error retrieving IMEIs', err.message);
                    cleanupAndResolve(err);
                    return;
                }

                console.log(`Found ${imeis.length} IMEIs to process for archiving`);

                if (imeis.length === 0) {
                    cleanupAndResolve();
                    return;
                }

                // Process IMEIs sequentially with proper Promise handling
                processImeiSequentially(imeis, 0)
                    .then(() => {
                        console.log('Archive process completed for all IMEIs');
                        cleanupAndResolve();
                    })
                    .catch(error => {
                        console.error('Error during archiving process:', error);
                        cleanupAndResolve(error);
                    });
            });
        }

        // Process IMEIs one by one
        async function processImeiSequentially(imeis, index) {
            if (index >= imeis.length) {
                return; // All IMEIs processed
            }

            const imei = imeis[index];
            console.log(`Processing IMEI ${imei} (${index + 1}/${imeis.length})`);

            try {
                // Process this IMEI and wait for completion
                await new Promise((resolveImei, rejectImei) => {
                    try {
                        // Create a wrapper to make archiveOldRecords return a Promise
                        const archiveWrapper = () => {
                            try {
                                // Track when this IMEI is done
                                const originalDb = db;
                                const originalTableName = `gps_data_${imei.replace(/[^a-zA-Z0-9_]/g, '')}`;

                                // Check every 10 seconds if the archiving is complete
                                const checkInterval = setInterval(() => {
                                    // Get memory usage during processing
                                    const memDuring = process.memoryUsage();
                                    console.log(`Memory usage during archiving IMEI ${imei}: ${Math.round(memDuring.rss / 1024 / 1024)}MB RSS`);

                                    // Check if the delete operation has been performed
                                    originalDb.get(`SELECT COUNT(*) as count FROM ${originalTableName} WHERE gpsTimestamp < date('now', '-${setDays} days')`, (err, result) => {
                                        if (err) {
                                            // Table might have been deleted or other error
                                            clearInterval(checkInterval);
                                            resolveImei();
                                        } else if (result && result.count === 0) {
                                            // No more old records, archiving is complete
                                            clearInterval(checkInterval);
                                            console.log(`Completed processing IMEI ${imei} (${index + 1}/${imeis.length})`);
                                            resolveImei();
                                        }
                                    });
                                }, 10000);

                                // Start the archiving process
                                archiveOldRecords(db, imei);
                            } catch (error) {
                                console.error(`Error in archive wrapper for IMEI ${imei}:`, error);
                                rejectImei(error);
                            }
                        };

                        // Add a small delay before starting
                        setTimeout(archiveWrapper, 1000);
                    } catch (error) {
                        console.error(`Error setting up archiving for IMEI ${imei}:`, error);
                        rejectImei(error);
                    }
                });

                // Force garbage collection if available (Node.js with --expose-gc flag)
                if (global.gc) {
                    console.log(`Running garbage collection after IMEI ${imei}`);
                    global.gc();
                }

                // Add a longer delay between processing each IMEI
                await new Promise(r => setTimeout(r, 10000));

                // Get memory usage after this IMEI
                const memAfter = process.memoryUsage();
                console.log(`Memory usage after IMEI ${imei}: ${Math.round(memAfter.rss / 1024 / 1024)}MB RSS`);

                // Process the next IMEI
                return processImeiSequentially(imeis, index + 1);
            } catch (error) {
                console.error(`Error processing IMEI ${imei}:`, error);
                // Continue with next IMEI despite error
                return processImeiSequentially(imeis, index + 1);
            }
        }

        // Helper to clean up and resolve the main promise
        function cleanupAndResolve(error) {
            // Get final memory usage
            const memFinal = process.memoryUsage();
            console.log(`Final memory usage: ${Math.round(memFinal.rss / 1024 / 1024)}MB RSS`);

            // Reset the flag with a timeout to prevent immediate restart
            setTimeout(() => {
                global.isMpuvArchivingInProgress = false;
                console.log('MPUV archiving process flag reset, ready for next run');
            }, 120000); // 2-minute timeout

            if (error) {
                reject(error);
            } else {
                resolve();
            }
        }

        // Start processing
        processImeis();
    });
}

// Database maintenance function to run VACUUM
async function performDatabaseMaintenance() {
    console.log('Starting database maintenance with VACUUM...');

    // Track if maintenance is already running
    if (global.isMpuvMaintenanceInProgress) {
        console.log('Maintenance already in progress, skipping this run');
        return;
    }

    global.isMpuvMaintenanceInProgress = true;

    try {
        // Get memory usage before starting
        const memBefore = process.memoryUsage();
        console.log(`Memory usage before maintenance: ${Math.round(memBefore.rss / 1024 / 1024)}MB RSS`);

        // 1. Vacuum the main database
        await vacuumDatabase(dbPath);

        // 2. Find and vacuum all archive databases
        await vacuumArchiveDatabases();

        console.log('Database maintenance completed successfully');
    } catch (error) {
        console.error('Error during database maintenance:', error);
    } finally {
        // Reset the maintenance flag after a delay
        setTimeout(() => {
            global.isMpuvMaintenanceInProgress = false;
            console.log('Maintenance flag reset, ready for next run');
        }, 60000); // 1-minute timeout
    }
}

// Helper function to vacuum a specific database
function vacuumDatabase(dbFilePath) {
    return new Promise((resolve, reject) => {
        console.log(`Vacuuming database: ${dbFilePath}`);

        // Open a dedicated connection for vacuum
        const vacuumDb = new sqlite3.Database(dbFilePath, sqlite3.OPEN_READWRITE, (err) => {
            if (err) {
                console.error(`Error opening database for vacuum: ${dbFilePath}`, err.message);
                reject(err);
                return;
            }

            // Get database size before vacuum
            const fs = require('fs');
            const sizeBefore = fs.statSync(dbFilePath).size / (1024 * 1024); // Size in MB
            console.log(`Database size before vacuum: ${sizeBefore.toFixed(2)}MB`);

            // Run VACUUM
            vacuumDb.run('VACUUM', (vacuumErr) => {
                if (vacuumErr) {
                    console.error(`Error vacuuming database: ${dbFilePath}`, vacuumErr.message);
                    vacuumDb.close();
                    reject(vacuumErr);
                    return;
                }

                // Get database size after vacuum
                const sizeAfter = fs.statSync(dbFilePath).size / (1024 * 1024); // Size in MB
                console.log(`Database size after vacuum: ${sizeAfter.toFixed(2)}MB`);
                console.log(`Space reclaimed: ${(sizeBefore - sizeAfter).toFixed(2)}MB`);

                // Run ANALYZE to update statistics
                vacuumDb.run('ANALYZE', (analyzeErr) => {
                    if (analyzeErr) {
                        console.error(`Error analyzing database: ${dbFilePath}`, analyzeErr.message);
                    } else {
                        console.log(`Database statistics updated for: ${dbFilePath}`);
                    }

                    // Close the connection
                    vacuumDb.close((closeErr) => {
                        if (closeErr) {
                            console.error(`Error closing database: ${dbFilePath}`, closeErr.message);
                            reject(closeErr);
                        } else {
                            console.log(`Vacuum completed for: ${dbFilePath}`);
                            resolve();
                        }
                    });
                });
            });
        });
    });
}

// Find and vacuum all archive databases
function vacuumArchiveDatabases() {
    return new Promise((resolve, reject) => {
        const fs = require('fs');
        const dbDir = path.resolve(__dirname, 'db');

        fs.readdir(dbDir, async (err, files) => {
            if (err) {
                console.error('Error reading database directory:', err.message);
                reject(err);
                return;
            }

            // Filter for archive database files
            const archiveDbFiles = files.filter(file =>
                file.startsWith('mpuvgps_archive_') && file.endsWith('.db')
            );

            console.log(`Found ${archiveDbFiles.length} archive databases to vacuum`);

            if (archiveDbFiles.length === 0) {
                resolve();
                return;
            }

            // Process each archive database with a delay between each
            for (let i = 0; i < archiveDbFiles.length; i++) {
                const dbFile = archiveDbFiles[i];
                const dbFilePath = path.resolve(dbDir, dbFile);

                try {
                    console.log(`Processing archive database ${i+1}/${archiveDbFiles.length}: ${dbFile}`);
                    await vacuumDatabase(dbFilePath);

                    // Add a delay between processing each database to prevent resource exhaustion
                    if (i < archiveDbFiles.length - 1) {
                        await new Promise(r => setTimeout(r, 5000));
                    }
                } catch (error) {
                    console.error(`Error vacuuming archive database: ${dbFile}`, error);
                    // Continue with next database despite error
                }
            }

            resolve();
        });
    });
}


// Schedule the archiving task to run at 2 AM Sunday
cron.schedule('0 2 * * SUN', async () => {
    console.log('Running scheduled archiving task at 2 AM');
    try {
        await archiveOldRecordsForAllImeis();
        console.log('Archive process completed successfully');
    } catch (error) {
        console.error('Error executing cron job:', error);
    } finally {
        // Ensure the flag is reset even if there's an error
        setTimeout(() => {
            if (global.isMpuvArchivingInProgress) {
                global.isMpuvArchivingInProgress = false;
                console.log('MPUV archiving process flag reset after error');
            }
        }, 120000); // 2-minute timeout
    }
}, {
    timezone: "Asia/Manila",
    scheduled: true
});

// Schedule the archiving task to run at 2 AM Sunday
cron.schedule('0 2 * * SUN', async () => {
    console.log('Running scheduled database maintenance task at 2 AM');
    try {
        await performDatabaseMaintenance();
    } catch (error) {
        console.error('Error executing maintenance cron job:', error);
    }
}, {
    timezone: "Asia/Manila",
    scheduled: true
});


// Load records of all imeis from database to imeiLastRecords
function loadRecords() {
    const db = getDbInstance();
    return new Promise((resolve, reject) => {
        db.all("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'gps_data_%'", [], (err, tables) => {
            if (err) {
                console.error('Error getting tables:', err);
                reject(err);
                return;
            }

            if (tables.length === 0) {
                console.log('No IMEI tables found in database');
                resolve();
                return;
            }

            let completedQueries = 0;
            const totalTables = tables.length;

            tables.forEach(table => {
                const imei = table.name.replace('gps_data_', '');

                const query = `
                    SELECT
                        '${imei}' as imei,
                        serverTimestamp,
                        gpsTimestamp,
                        latitude,
                        longitude,
                        speed,
                        course,
                        satelliteCount,
                        accCode
                    FROM ${table.name}
                    ORDER BY gpsTimestamp DESC
                    LIMIT 1
                `;

                db.get(query, [], (err, row) => {
                    completedQueries++;

                    if (err) {
                        console.error(`Error getting last record for IMEI ${imei}:`, err);
                    } else if (row) {
                        imeiLastRecords.set(imei, row);
                    }

                    if (completedQueries === totalTables) {
                        console.log(`Loaded ${imeiLastRecords.size} IMEI records from database`);
                        resolve();
                    }
                });
            });
        });
    });
}

// Cleanup function for proper shutdown
function cleanup() {
    if (dbInstance) {
        dbInstance.close((err) => {
            if (err) {
                console.error('Error closing database:', err.message);
            } else {
                console.log('Database connection closed.');
            }
        });
        dbInstance = null;
    }
}

// Initialize database and load records
async function initialize() {
    try {
        await loadRecords();
    } catch (err) {
        console.error('Error during initialization:', err);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    cleanup();
    process.exit();
});

// Run initialization
initialize();

module.exports = {
    getDbInstance,
    getArchiveDbInstance,
    createTableForIMEI,
    imeiLastRecords,
    cleanup
};