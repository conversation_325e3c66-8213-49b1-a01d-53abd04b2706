<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iNav Fleet Summary</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #F4F8FA;
            color: #061238;
            text-align: center;
        }

        .content {
            height: 40px;
            padding: 5px;
            align-items: center;
            justify-content: space-between;
        }

        .content h1 {
            color: #0A4951;
            font-size: 15px;
            padding: 10px;
        }

        .row {
            display: flex;
            height: calc(100% - 60px);
            border-top: 1px solid #DAE6EF;
        }

        /* Mobile layout */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 0;
                display: none;
            }

            .toggle-button {
                display: block;
            }
        }

        .material-icons-outlined {
            vertical-align: middle;
        }

        .toggle-button {
            display: block;
            position: absolute;
            top: 5px;
            left: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .toggle-button:hover {
            background-color: #DAE6EF;
        }

        .date-picker-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .date-picker {
            margin-bottom: 30px;
            flex: 1;
        }

        .date-range {
            position: absolute;
            left: 60px;
            display: inline-block;
            text-align: center;
            color: #156E7F;
            font-size: 11px;
            padding: 5px;
            border-radius: 8px;
            background: white;
            border: 1px solid #DAE6EF;
            width: 120px;
        }

        .flatpickr-calendar {
            font-size: 11px;
        }

        .flatpickr-months {
            font-size: 11px
        }

        .download-button {
            display: block;
            margin: 20px auto;
            padding: 5px 20px;
            font-size: 12px;
            background-color: #3BB3C3;
            color: #fff;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            margin-right: 10px;
        }

        .download-button:hover {
            background-color: #156E7F;
        }

        #reportContent {
            font-size: 15px;
        }

        /* Sidebar container */
        .sidebar-container {
            display: flex;
            flex-direction: column;
            background-color: #f4f6f9;
            width: 300px;
            max-height: 100vh;
            overflow-y: auto;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-family: 'Arial', sans-serif;
            color: #333;
            border-right: 1px solid #DAE6EF;
            font-size: 13px;
        }

        .sidebar-item {
            padding: 10px;
            margin: 5px 0;
            background-color: #fff;
            border-radius: 5px;
            cursor: pointer;
            text-align: left;
        }

        .sidebar-item:hover {
            background-color: #DAE6EF;
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 12px;
            text-align: left;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        h2.table-title {
            font-size: 18px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        table thead {
            background-color: #0A4951;
            color: white;
            position: sticky;
            top: -20px;
        }

        table thead th {
            padding: 12px 15px;
            text-align: center;
        }

        table tbody tr {
            border-bottom: 1px solid #DAE6EF;
        }

        table tbody tr:nth-of-type(even) {
            background-color: #F4F8FA;
        }

        table tbody tr:hover {
            background-color: #DAE6EF;
            cursor: pointer;
        }

        table tbody td {
            padding: 12px 15px;
            color: #061238;
        }

        table tbody td:last-child {
            text-align: center;
        }

        .spinner {
            border: 4px solid #F9FBFD;
            border-top: 4px solid #3BB3C3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translate(-50%, -50%);
            z-index: 1100;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

</head>

<body>
    <div class="content">
        <h1>iNav Fleet Summary</h1>
        <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">
            <i class="material-icons-outlined">view_sidebar</i>
        </button>
    </div>

    <div class="row">
        <div class="sidebar-container" id="sidebar">
            <div class="sidebar-item" onclick="setReportType('Stop Summary')">Stop Summary</div>
            <div class="sidebar-item" onclick="setReportType('Trip Summary')">Trip Summary</div>
            <div class="sidebar-item" onclick="setReportType('Geofence Summary')">Geofence Summary</div>
            <div class="sidebar-item" onclick="setReportType('Fuel Summary')">Fuel Summary</div>
            <div class="sidebar-item" onclick="setReportType('iButton Summary')">iButton Summary</div>
            <div class="sidebar-item" onclick="setReportType('SOS Summary')">SOS Summary</div>
            <div class="sidebar-item" onclick="setReportType('Alarm Summary')">Alarm Summary</div>
        </div>

        <div class="main-content">

            <div class="date-picker-container">
                <!-- Date Range Picker -->
                <div class="date-picker">
                    <input type="text" id="dateRangePicker" class="date-range" placeholder="Select Date Range">
                </div>

                <!-- Download Button -->
                <button class="download-button" onclick="downloadCSV()">Download CSV</button>
            </div>

            <!-- Report Display Area -->
            <div id="reportContent">
                <p>Please select a report from the sidebar.</p>
            </div>

            <!-- Spinner -->
            <div id="spinner" class="spinner" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentReportType = null;
        let startUTC, endUTC;
        const filterStopDuration = 3; // in minutes
        const filterStopDistance = 100; // in meters

        let options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
            timeZone: 'Asia/Manila'
        };

        function setReportType(reportType) {
            currentReportType = reportType;
            loadReport(currentReportType);
        }

        // Toggle the sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.querySelector('.main-content');
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '50%';
                    sidebar.style.display = 'flex';
                    content.style.width = '50%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    content.style.width = '100%';
                }
            } else {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '22.95%';
                    sidebar.style.display = 'flex';
                    content.style.width = '77.05%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    content.style.width = '100%';
                }
            }
        }

        // Initialize Date Range Picker with today's date as default
        const today = new Date();
        //const formattedToday = today.toISOString().split("T")[0]; // YYYY-MM-DD format
        const formattedToday = `${today.toLocaleString('default', { month: 'short' })}-${today.getDate()}`; // M-d format

        flatpickr("#dateRangePicker", {
            mode: "range",
            //dateFormat: "Y-m-d",
            dateFormat: "M-d",
            defaultDate: [formattedToday, formattedToday], // Default to today
            onChange: function (selectedDates, dateStr, instance) {
                // Trigger plotTrackData when date range changes
                if (selectedDates.length === 2) {
                    startUTC = selectedDates[0].toISOString();
                    endUTC = new Date(selectedDates[1].getFullYear(), selectedDates[1].getMonth(), selectedDates[1].getDate(), 23, 59, 59).toISOString();
                    if (currentReportType) {
                        loadReport(currentReportType);
                    }
                }
            }
        });

        const urlParams = new URLSearchParams(window.location.search);
        const port = urlParams.get('port');

        let accumulatedUserData = {};
        async function loadAccumulatedUserData() {
            const storedData = localStorage.getItem('accumulatedUserData');
            if (storedData) {
                accumulatedUserData = JSON.parse(storedData);
                console.log('Data loaded from localStorage');
            }
        }

        window.onload = async function () {
            await loadAccumulatedUserData();
        };

        // Fetch track data for the given IMEI and date range
        async function fetchIbuttonData() {

            const url = `http://dev.inavcloud.com:${port}/api/ibutton/last`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error(`Error fetching track data:`, error);
                return null;
            }
        }

        // Fetch track data for the given IMEI and date range
        async function fetchSosData() {

            const url = `http://dev.inavcloud.com:${port}/api/sos/all?startDate=${startUTC}&endDate=${endUTC}`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error(`Error fetching track data:`, error);
                return null;
            }
        }

        // Fetch track data for the given IMEI and date range
        async function fetchAlarmData() {

            const url = `http://dev.inavcloud.com:${port}/api/alarm/last`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error(`Error fetching track data:`, error);
                return null;
            }
        }

        // Initial plot with today's date
        startUTC = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0).toISOString();
        endUTC = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString();

        // Function to load report content
        async function loadReport(reportType) {
            const spinner = document.getElementById('spinner');
            const reportContent = document.getElementById('reportContent');
            reportContent.innerHTML = `<p>Loading: ${reportType}...</p>`;

            try {
                // Show spinner while data is loading
                spinner.style.display = 'block';

                let reportHtml = '';

                switch (reportType) {
                    case 'iButton Summary':
                        const ibuttonData = await fetchIbuttonData();
                        reportHtml = await createIbuttonSummary(ibuttonData);
                        break;
                    case 'SOS Summary':
                        const sosData = await fetchSosData();
                        reportHtml = await createSosSummary(sosData);
                        break;
                    case 'Alarm Summary':
                        const alarmData = await fetchAlarmData();
                        reportHtml = await createAlarmSummary(alarmData);
                        break;
                    default:
                        reportHtml = '<p>No summary type selected.</p>';
                        break;
                }

                reportContent.innerHTML = reportHtml;

            } catch (error) {
                console.error('Error loading summary:', error);
                reportContent.innerHTML = `<p>Failed to load the ${reportType}. Please try again later.</p>`;
            } finally {
                // Hide spinner after data is loaded or if an error occurs
                spinner.style.display = 'none';
            }
        }

        async function createIbuttonSummary(data) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">iButton Summary</h2>
                <table border="1">
                <thead>
                <tr>
                <th>No</th>
                <th>iButton ID</th>
                <th>Name</th>
                <th>GPS Timestamp</th>
                <th>Location</th>
                <th>Address</th>
                <th>Satellite</th>
                <th>Speed</th>
                <th>Ignition</th>
                <th>In Status</th>
                <th>Out Status</th>
                <th>Ext Battery</th>
                <th>Int Battery</th>
                </tr>
                </thead>
                <tbody>
            `;

            for (const [index, records] of data.entries()) {
                const { imei, gpsTimestamp, latitude, longitude, speed, satelliteCount, inStatus, accCode, outStatus, extBat, intBat, iButton } = records;

                // Ensure imei exists in accumulatedUserData
                const imeiData = accumulatedUserData[imei] || {};
                const name = imeiData.name || imei;

                // Format GPS timestamp
                const gpsTimestampFormatted = new Date(gpsTimestamp.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                    <td>${index + 1}</td>
                    <td>${iButton}</td>
                    <td>${name}</td>
                    <td>${gpsTimestampFormatted}</td>
                    <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                    <td id="address-${index}">${addressPlaceholder}</td>
                    <td>${satelliteCount}</td>
                    <td>${speed}</td>
                    <td>${accCode}</td>
                    <td>${inStatus}</td>
                    <td>${outStatus}</td>
                    <td>${extBat}</td>
                    <td>${intBat}</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${index}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }

        async function createSosSummary(data) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">SOS Summary</h2>
                <table border="1">
                <thead>
                <tr>
                <th>No</th>
                <th>Name</th>
                <th>SOS Count</th>
                <th>GPS Timestamp</th>
                <th>Location</th>
                <th>Address</th>
                <th>Satellite</th>
                <th>Speed</th>
                <th>Ignition</th>
                <th>In Status</th>
                <th>Out Status</th>
                <th>Ext Battery</th>
                <th>Int Battery</th>
                </tr>
                </thead>
                <tbody>
            `;

            for (const [index, records] of data.entries()) {
                const { imei, gpsTimestamp, latitude, longitude, speed, satelliteCount, inStatus, accCode, sosCount, outStatus, extBat, intBat } = records;

                // Ensure imei exists in accumulatedUserData
                const imeiData = accumulatedUserData[imei] || {};
                const name = imeiData.name || imei;

                // Format GPS timestamp
                const gpsTimestampFormatted = new Date(gpsTimestamp.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                    <td>${index + 1}</td>
                    <td>${name}</td>
                    <td>${sosCount}</td>
                    <td>${gpsTimestampFormatted}</td>
                    <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                    <td id="address-${index}">${addressPlaceholder}</td>
                    <td>${satelliteCount}</td>
                    <td>${speed}</td>
                    <td>${accCode}</td>
                    <td>${inStatus}</td>
                    <td>${outStatus}</td>
                    <td>${extBat}</td>
                    <td>${intBat}</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${index}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }

        async function createAlarmSummary(data) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">Alarm Summary</h2>
                <table border="1">
                <thead>
                <tr>
                <th>No</th>
                <th>Alarm</th>
                <th>Name</th>
                <th>GPS Timestamp</th>
                <th>Location</th>
                <th>Address</th>
                <th>Satellite</th>
                <th>Speed</th>
                <th>Ignition</th>
                <th>In Status</th>
                <th>Out Status</th>
                <th>Ext Battery</th>
                <th>Int Battery</th>
                </tr>
                </thead>
                <tbody>
            `;

            for (const [index, records] of data.entries()) {
                const { imei, gpsTimestamp, latitude, longitude, speed, satelliteCount, inStatus, accCode, outStatus, extBat, intBat, iButton, alarmCode } = records;

                // Ensure imei exists in accumulatedUserData
                const imeiData = accumulatedUserData[imei] || {};
                const name = imeiData.name || imei;

                // Format GPS timestamp
                const gpsTimestampFormatted = new Date(gpsTimestamp.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                    <td>${index + 1}</td>
                    <td>${alarmCode}</td>
                    <td>${name}</td>
                    <td>${gpsTimestampFormatted}</td>
                    <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                    <td id="address-${index}">${addressPlaceholder}</td>
                    <td>${satelliteCount}</td>
                    <td>${speed}</td>
                    <td>${accCode}</td>
                    <td>${inStatus}</td>
                    <td>${outStatus}</td>
                    <td>${extBat}</td>
                    <td>${intBat}</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${index}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }

        async function getAddress(lat, lng) {
            const url = `http://dev.inavcloud.com:3100/api/reverse-geocode?lat=${lat}&lng=${lng}`;

            try {
                const response = await fetch(url);
                const data = await response.json();

                if (data.address) {
                    return data.address;
                } else {
                    console.log('No results found');
                    return null;
                }
            } catch (error) {
                console.error('Error:', error);
                return null;
            }
        }

        function downloadCSV() {
            const table = document.getElementById('reportContent');
            const rows = table.querySelectorAll('tr');
            let csvContent = '';

            rows.forEach(row => {
                const cells = row.querySelectorAll('th, td');
                const rowContent = Array.from(cells).map(cell => `"${cell.innerText.replace(/"/g, '""')}"`).join(',');
                csvContent += rowContent + '\n';
            });

            // Create a Blob object with the CSV data
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a link element, set its href to the Blob URL, and trigger a download
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            const startUTCInManila = new Date(startUTC).toLocaleString('en-US', options).replace(/, /g, '_');
            const endUTCInManila = new Date(endUTC).toLocaleString('en-US', options).replace(/, /g, '_');
            link.setAttribute('download', `${currentReportType}_${startUTCInManila}_${endUTCInManila}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

    </script>

</body>

</html>