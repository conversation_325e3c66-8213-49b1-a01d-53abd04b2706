<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iNav Vehicle Reports</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #F4F8FA;
            color: #061238;
            text-align: center;
        }

        .content {
            height: 40px;
            padding: 5px;
            align-items: center;
            justify-content: space-between;
        }

        .content h1 {
            color: #0A4951;
            font-size: 15px;
            padding: 10px;
        }

        .row {
            display: flex;
            height: calc(100% - 60px);
            border-top: 1px solid #DAE6EF;
        }

        /* Mobile layout */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 0;
                display: none;
            }

            .toggle-button {
                display: block;
            }
        }

        .material-icons-outlined {
            vertical-align: middle;
        }

        .toggle-button {
            display: block;
            position: absolute;
            top: 5px;
            left: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .toggle-button:hover {
            background-color: #DAE6EF;
        }

        .date-picker-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .date-picker {
            margin-bottom: 30px;
            flex: 1;
        }

        .date-range {
            position: absolute;
            left: 60px;
            display: inline-block;
            text-align: center;
            color: #156E7F;
            font-size: 11px;
            padding: 5px;
            border-radius: 8px;
            background: white;
            border: 1px solid #DAE6EF;
            width: 180px;
        }

        .flatpickr-calendar {
            font-size: 11px;
        }

        .flatpickr-months {
            font-size: 11px
        }

        .download-button {
            display: block;
            margin: 20px auto;
            padding: 5px 20px;
            font-size: 12px;
            background-color: #3BB3C3;
            color: #fff;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            margin-right: 10px;
        }

        .download-button:hover {
            background-color: #156E7F;
        }

        #reportContent {
            font-size: 15px;
        }

        /* Sidebar container */
        .sidebar-container {
            display: flex;
            flex-direction: column;
            background-color: #f4f6f9;
            width: 300px;
            max-height: 100vh;
            overflow-y: auto;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-family: 'Arial', sans-serif;
            color: #333;
            border-right: 1px solid #DAE6EF;
            font-size: 13px;
        }

        .sidebar-item {
            padding: 10px;
            margin: 5px 0;
            background-color: #fff;
            border-radius: 5px;
            cursor: pointer;
            text-align: left;
        }

        .sidebar-item:hover {
            background-color: #DAE6EF;
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 12px;
            text-align: left;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        h2.table-title {
            font-size: 18px;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        table thead {
            background-color: #0A4951;
            color: white;
            position: sticky;
            top: -20px;
        }

        table thead th {
            padding: 12px 15px;
            text-align: center;
        }

        table tbody tr {
            border-bottom: 1px solid #DAE6EF;
        }

        table tbody tr:nth-of-type(even) {
            background-color: #F4F8FA;
        }

        table tbody tr:hover {
            background-color: #DAE6EF;
            cursor: pointer;
        }

        table tbody td {
            padding: 12px 15px;
            color: #061238;
        }

        table tbody td:last-child {
            text-align: center;
        }

        .spinner {
            border: 4px solid #F9FBFD;
            border-top: 4px solid #3BB3C3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            right: 50%;
            transform: translate(-50%, -50%);
            z-index: 1100;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

</head>

<body>
    <div class="content">
        <h1>iNav Vehicle Reports</h1>
        <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">
            <i class="material-icons-outlined">view_sidebar</i>
        </button>
    </div>

    <div class="row">
        <div class="sidebar-container" id="sidebar">
            <div class="sidebar-item" onclick="setReportType('Stop Report')">Stop Report</div>
            <div class="sidebar-item" onclick="setReportType('Trip Report by Stop')">Trip Report by Stop</div>
            <div class="sidebar-item" onclick="setReportType('Trip Report by Ignition')">Trip Report by Ignition</div>
            <div class="sidebar-item" onclick="setReportType('Geofence Report for Bus')">Geofence Report for Bus</div>
            <div class="sidebar-item" onclick="setReportType('Fuel Report')">Fuel Report</div>
            <div class="sidebar-item" onclick="setReportType('Alarm Report')">Alarm Report</div>
        </div>

        <div class="main-content">

            <div class="date-picker-container">
                <!-- Date Range Picker -->
                <div class="date-picker">
                    <input type="text" id="dateRangePicker" class="date-range" placeholder="Select Date Range">
                </div>

                <!-- Download Button -->
                <button class="download-button" onclick="downloadCSV()">Download CSV</button>
            </div>

            <!-- Report Display Area -->
            <div id="reportContent">
                <p>Please select a report from the sidebar.</p>
            </div>

            <!-- Spinner -->
            <div id="spinner" class="spinner" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentReportType = null;
        let startUTC, endUTC;

        let options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
            timeZone: 'Asia/Manila'
        };

        function setReportType(reportType) {
            currentReportType = reportType;
            loadReport(currentReportType);
        }

        // Toggle the sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.querySelector('.main-content');
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '50%';
                    sidebar.style.display = 'flex';
                    content.style.width = '50%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    content.style.width = '100%';
                }
            } else {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '22.95%';
                    sidebar.style.display = 'flex';
                    content.style.width = '77.05%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    content.style.width = '100%';
                }
            }
        }

        // Initialize Date Range Picker with today's date as default
        const today = new Date();
        const formattedToday = `${today.toLocaleString('default', { month: 'short' })}-${today.getDate()}`; // M-d format

        flatpickr("#dateRangePicker", {
            mode: "range",
            dateFormat: "M-d H:i", // Include time in the format
            enableTime: true, // Enable time selection
            time_24hr: true, // Use 24-hour format
            defaultHour: 0, // Default start and end hours
            defaultMinute: 0, // Default start and end minutes
            defaultDate: [
                new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0), // Start of today
                new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59) // End of today
            ],

            onChange: function (selectedDates, dateStr, instance) {
                if (selectedDates.length == 2 && (selectedDates[1].getHours() === 0 || selectedDates[1].getHours() === 23)) {
                    selectedDates[1].setHours(23, 59, 0, 0);
                    instance.setDate(selectedDates, false); // Force flatpickr to update
                }
                console.log(selectedDates);
            },
            onClose: function (selectedDates, dateStr, instance) {
                // Trigger plotTrackData when date range changes
                if (selectedDates.length === 2) {
                    startUTC = selectedDates[0].toISOString();
                    endUTC = selectedDates[1].toISOString();
                    if (currentReportType) {
                        loadReport(currentReportType);
                    }
                }
            }
        });

        const urlParams = new URLSearchParams(window.location.search);
        const imei = urlParams.get('imei');
        const port = urlParams.get('port');
        const name = urlParams.get('name');
        const calibration = urlParams.get('calibration');
        //const name = 'Hello';
        //const imei = '860896052076320';
        //const port = '3005';

        function csvToFuelTable(csv) {
            const values = csv.split(',').map(Number); // Split and convert to numbers
            const fuelTable = [];

            for (let i = 0; i < values.length; i += 2) {
                fuelTable.push({ litres: values[i], sensor: values[i + 1] });
            }

            return fuelTable;
        }

        const fuelTable = csvToFuelTable(calibration);

        // Function to calculate fuel level given a sensor reading
        function getFuelLevel(sensorReading) {

            // Find the two closest points
            let lowerPoint = fuelTable[0];
            let upperPoint = fuelTable[fuelTable.length - 1];

            for (let i = 0; i < fuelTable.length - 1; i++) {
                if (sensorReading >= fuelTable[i].sensor && sensorReading <= fuelTable[i + 1].sensor) {
                    lowerPoint = fuelTable[i];
                    upperPoint = fuelTable[i + 1];
                    break;
                }
            }

            // Perform linear interpolation
            const interpolatedLitres = lowerPoint.litres +
                ((sensorReading - lowerPoint.sensor) * (upperPoint.litres - lowerPoint.litres)) /
                (upperPoint.sensor - lowerPoint.sensor);

            return interpolatedLitres;
        }

        // Fetch track data for the given IMEI and date range
        async function fetchTrackData(imei, startDate, endDate) {

            const url = `http://dev.inavcloud.com:${port}/api/messages?imei=${imei}&startDate=${startDate}&endDate=${endDate}`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error(`Error fetching track data:`, error);
                return null;
            }
        }

        // Fetch track data for the given IMEI and date range
        async function fetchAlarmData(imei, startDate, endDate) {

            const url = `http://dev.inavcloud.com:${port}/api/alarm?imei=${imei}&startDate=${startDate}&endDate=${endDate}`;
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error(`Error fetching track data:`, error);
                return null;
            }
        }

        // Initial plot with today's date
        startUTC = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0).toISOString();
        endUTC = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString();

        // Function to load report content
        async function loadReport(reportType) {
            const spinner = document.getElementById('spinner');
            const reportContent = document.getElementById('reportContent');
            reportContent.innerHTML = `<p>Loading: ${reportType}...</p>`;

            try {
                // Show spinner while data is loading
                spinner.style.display = 'block';

                let reportHtml = '';
                const data = await fetchTrackData(imei, startUTC, endUTC);
                data.sort((a, b) => a.gpsTimestamp - b.gpsTimestamp);
                const filterStopDuration = 3; // in minutes
                const filterStopDistance = 100; // in meters
                const stopLocations = extractStopLocations(data, filterStopDuration, filterStopDistance);
                const accStopLocations = extractAccStopLocations(data, filterStopDuration);

                switch (reportType) {
                    case 'Stop Report':
                        reportHtml = await createStopReport(stopLocations);
                        break;
                    case 'Trip Report by Stop':
                        reportHtml = await createTripReport(stopLocations);
                        break;
                    case 'Trip Report by Ignition':
                        reportHtml = await createTripReport(accStopLocations);
                        break;
                    case 'Geofence Report for Bus':
                        const filterStopDurationForBus = 0; // in minutes
                        const filterStopDistanceForBus = 100; // in meters
                        const stopLocationsForBus = extractStopLocations(data, filterStopDurationForBus, filterStopDistanceForBus);
                        reportHtml = await createGeofenceReportForBus(stopLocations, stopLocationsForBus, reportType);
                        break;
                    case 'Fuel Report':
                        reportHtml = await createFuelReport(stopLocations);
                        break;
                    case 'Alarm Report':
                        const alarmData = await fetchAlarmData(imei, startUTC, endUTC);
                        reportHtml = await createAlarmReport(alarmData);
                        break;
                    default:
                        reportHtml = '<p>No report type selected.</p>';
                        break;
                }

                reportContent.innerHTML = reportHtml;

            } catch (error) {
                console.error('Error loading report:', error);
                reportContent.innerHTML = `<p>Failed to load the ${reportType}. Please try again later.</p>`;
            } finally {
                // Hide spinner after data is loaded or if an error occurs
                spinner.style.display = 'none';
            }
        }

        function extractStopLocations(data, filterStopDuration, filterStopDistance) {
            const stopLocations = [];
            let stopLocation = null;
            let lastAccCode = null;
            let stopStartTime = null;
            let lastGpsTimestamp = null;
            let lastGpsLocation = null;
            let speedInt = 0;
            let reducedFilterStopDuration = filterStopDuration;

            let fuelLevelFloat = 0;
            let startFuelLevel = 0;

            let distanceFromLastStop = 0;
            let maxSpeed = 0;
            let totalSpeed = 0;
            let count = 0;

            for (let i = 0; i < data.length; i++) {
                const { gpsTimestamp, latitude, longitude, speed, accCode, fuelLevel, temperature } = data[i];
                const gpsTimeDiff = lastGpsTimestamp ? (new Date(gpsTimestamp).getTime() - new Date(lastGpsTimestamp).getTime()) / 1000 : 0;
                const gpsDistance = lastGpsLocation ? Math.sqrt(Math.pow(lastGpsLocation.latitude - latitude, 2) + Math.pow(lastGpsLocation.longitude - longitude, 2)) * 111 * 1000 : 0;
                const gpsSpeed = gpsTimeDiff > 0 ? gpsDistance / gpsTimeDiff * 3.6 : 0;

                speedInt = parseInt(speed);

                if (gpsTimeDiff >= filterStopDuration * 60) {
                    speedInt = gpsSpeed;
                    reducedFilterStopDuration = filterStopDuration; // * 0.5;
                }

                distanceFromLastStop += gpsDistance;
                if (speedInt > maxSpeed) maxSpeed = speedInt;

                if (speedInt) {
                    totalSpeed += speedInt;
                    count++;
                }

                fuelLevelFloat = parseFloat(getFuelLevel(fuelLevel));

                const dist = stopLocation ? Math.sqrt(Math.pow(stopLocation.latitude - latitude, 2) + Math.pow(stopLocation.longitude - longitude, 2)) * 111 * 1000 : 0;

                if (stopLocation && (speedInt > 5 && dist > filterStopDistance)) {
                    const stopDuration = (new Date(lastGpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000; // in minutes
                    if (stopDuration >= reducedFilterStopDuration) {
                        const averageSpeed = totalSpeed / count;
                        stopLocations.push({
                            ...stopLocation,
                            stopStartTime,
                            stopEndTime: lastGpsTimestamp,
                            stopDuration,
                            lastAccCode,
                            distanceFromLastStop,
                            maxSpeed,
                            averageSpeed,
                            startFuelLevel,
                            endFuelLevel: fuelLevelFloat,
                        });
                        reducedFilterStopDuration = filterStopDuration;
                        distanceFromLastStop = 0; // Reset distance for the next trip
                        maxSpeed = 0; // Reset max speed for the next trip
                        totalSpeed = 0; // Reset total speed for the next trip
                        count = 0; // Reset count for the next trip

                    }
                    stopLocation = null;
                    stopStartTime = null;
                }

                if (!stopLocation && (speedInt <= 5 || (speedInt <= 5 && accCode === '0'))) {
                    // New stop detected
                    stopLocation = { latitude, longitude };
                    stopStartTime = gpsTimestamp;
                    lastAccCode = accCode;
                    startFuelLevel = fuelLevelFloat;
                }

                lastGpsTimestamp = gpsTimestamp;
                lastGpsLocation = { latitude, longitude };
            }

            // Handle last stop if the device is stopped at the end
            if (stopLocation) {
                const stopDuration = (new Date(data[data.length - 1].gpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000;
                const averageSpeed = totalSpeed / count;
                stopLocations.push({
                    ...stopLocation,
                    stopStartTime,
                    stopEndTime: data[data.length - 1].gpsTimestamp,
                    stopDuration,
                    lastAccCode: data[data.length - 1].accCode,
                    distanceFromLastStop,
                    maxSpeed,
                    averageSpeed,
                    startFuelLevel,
                    endFuelLevel: fuelLevelFloat,
                });
            }
            console.log('stopLocations:', stopLocations);
            return stopLocations;
        }

        function extractAccStopLocations(data, filterStopDuration) {
            const accLocations = [];
            let stopLocation = null;
            let lastAccCode = null;
            let accStartTime = null;
            let lastGpsTimestamp = null;
            let lastGpsLocation = null;
            let speedInt = 0;

            let fuelLevelFloat = 0;
            let startFuelLevel = 0;

            let distanceFromLastStop = 0;
            let maxSpeed = 0;
            let totalSpeed = 0;
            let count = 0;

            for (let i = 0; i < data.length; i++) {
                const { gpsTimestamp, latitude, longitude, speed, accCode, fuelLevel, temperature } = data[i];
                const gpsTimeDiff = lastGpsTimestamp ? (new Date(gpsTimestamp).getTime() - new Date(lastGpsTimestamp).getTime()) / 1000 : 0;
                const gpsDistance = lastGpsLocation ? Math.sqrt(Math.pow(lastGpsLocation.latitude - latitude, 2) + Math.pow(lastGpsLocation.longitude - longitude, 2)) * 111 * 1000 : 0;

                speedInt = parseInt(speed);

                distanceFromLastStop += gpsDistance;
                if (speedInt > maxSpeed) maxSpeed = speedInt;

                if (speedInt) {
                    totalSpeed += speedInt;
                    count++;
                }

                fuelLevelFloat = parseFloat(getFuelLevel(fuelLevel));

                const dist = stopLocation ? Math.sqrt(Math.pow(stopLocation.latitude - latitude, 2) + Math.pow(stopLocation.longitude - longitude, 2)) * 111 * 1000 : 0;

                if (stopLocation && accCode === '1') {
                    const stopDuration = (new Date(lastGpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000; // in minutes

                    if (stopDuration >= filterStopDuration) {
                        const averageSpeed = totalSpeed / count;
                        accLocations.push({
                            ...stopLocation,
                            stopStartTime,
                            stopEndTime: lastGpsTimestamp,
                            stopDuration,
                            lastAccCode,
                            distanceFromLastStop,
                            maxSpeed,
                            averageSpeed,
                            startFuelLevel,
                            endFuelLevel: fuelLevelFloat,
                        });
                        distanceFromLastStop = 0; // Reset distance for the next trip
                        maxSpeed = 0; // Reset max speed for the next trip
                        totalSpeed = 0; // Reset total speed for the next trip
                        count = 0; // Reset count for the next trip

                    }
                    stopLocation = null;
                    stopStartTime = null;
                }

                if (!stopLocation && accCode === '0') {
                    // New stop detected
                    stopLocation = { latitude, longitude };
                    stopStartTime = gpsTimestamp;
                    lastAccCode = accCode;
                    startFuelLevel = fuelLevelFloat;
                }

                lastGpsTimestamp = gpsTimestamp;
                lastGpsLocation = { latitude, longitude };
            }

            // Handle last stop if the device is stopped at the end
            if (stopLocation) {
                const stopDuration = (new Date(data[data.length - 1].gpsTimestamp).getTime() - new Date(stopStartTime).getTime()) / 60 / 1000;
                const averageSpeed = totalSpeed / count;

                accLocations.push({
                    ...stopLocation,
                    stopStartTime,
                    stopEndTime: data[data.length - 1].gpsTimestamp,
                    stopDuration,
                    lastAccCode: data[data.length - 1].accCode,
                    distanceFromLastStop,
                    maxSpeed,
                    averageSpeed,
                    startFuelLevel,
                    endFuelLevel: fuelLevelFloat,
                });
            }
            console.log('accLocations:', accLocations);
            return accLocations;
        }

        async function createStopReport(stopLocations) {

            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">${currentReportType} for ${name} IMEI: ${imei}</h2>
                <table border="1">
                <thead>
                <tr>
                    <th>Stop No</th>
                    <th>Location</th>
                    <th>Address</th>
                    <th>Stop Start Time</th>
                    <th>Stop End Time</th>
                    <th>Stop Duration</th>
                    <th>Total Stop Duration</th>
                    <th>Ignition</th>
                </tr>
                </thead>
                <tbody>
            `;

            let stopDurationTotal = 0;
            for (const [index, location] of stopLocations.entries()) {
                const { latitude, longitude, stopStartTime, stopEndTime, stopDuration, lastAccCode } = location;

                // Format stopStartTime, stopEndTime and stopDuration
                const stopStartTimeFormatted = new Date(stopStartTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const stopEndTimeFormatted = new Date(stopEndTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const hours = Math.floor(stopDuration / 60);
                const minutes = Math.round(stopDuration % 60);
                const stopDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${Math.round(stopDuration)} min`;

                stopDurationTotal += stopDuration;
                const days = Math.floor(stopDurationTotal / (24 * 60)); // Total days
                const remainingSeconds = stopDurationTotal % (24 * 60); // Remaining seconds after full days
                const hoursTotal = Math.floor(remainingSeconds / 60); // Remaining hours
                const minutesTotal = Math.round((remainingSeconds % 60) / 60); // Remaining minutes
                const stopDurationTotalFormatted = days >= 1
                    ? `${days} day${days > 1 ? 's' : ''} ${hoursTotal} hr ${minutesTotal} min`
                    : hoursTotal >= 1
                        ? `${hoursTotal} hr ${minutesTotal} min`
                        : `${Math.round(stopDurationTotal / 60)} min`; // Total minutes if less than an hour


                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                        <td id="address-${index}">${addressPlaceholder}</td>
                        <td>${stopStartTimeFormatted}</td>
                        <td>${stopEndTimeFormatted}</td>
                        <td>${stopDurationFormatted}</td>
                        <td>${stopDurationTotalFormatted}</td>
                        <td>${lastAccCode}</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${index}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }


        async function createTripReport(stopLocations) {

            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">${currentReportType} for ${name} IMEI: ${imei}</h2>
                <table border="1">
                <thead>
                <tr>
                    <th>Trip No</th>
                    <th>Location</th>
                    <th>Address</th>
                    <th>Trip Start Time</th>
                    <th>Trip End Time</th>
                    <th>Trip Duration</th>
                    <th>Trip Distance</th>
                    <th>Total Distance</th>
                    <th>Max Speed</th>
                    <th>Average Speed</th>
                </tr>
                </thead>
                <tbody>
            `;

            let tripDistanceTotal = 0;
            for (let i = 0; i < stopLocations.length - 1; i++) {
                const { latitude, longitude, stopEndTime, lastAccCode } = stopLocations[i];
                const { stopStartTime, distanceFromLastStop, maxSpeed, averageSpeed } = stopLocations[i + 1];

                // Format tripStartTime, tripEndTime and tripDuration
                const tripStartTimeFormatted = new Date(stopEndTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripEndTimeFormatted = new Date(stopStartTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripDuration = (new Date(stopStartTime) - new Date(stopEndTime)) / 60000; // duration in minutes
                const hours = Math.floor(tripDuration / 60);
                const minutes = Math.round(tripDuration % 60);
                const tripDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${Math.round(tripDuration)} min`;
                const tripDistance = `${(distanceFromLastStop / 1000).toFixed(2)} km`;
                tripDistanceTotal += distanceFromLastStop / 1000;
                const maxSpd = maxSpeed.toFixed(0);
                const avgSpd = averageSpeed.toFixed(0);

                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                        <td>${i + 1}</td>
                        <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                        <td id="address-${i}">${addressPlaceholder}</td>
                        <td>${tripStartTimeFormatted}</td>
                        <td>${tripEndTimeFormatted}</td>
                        <td>${tripDurationFormatted}</td>
                        <td>${tripDistance}</td>
                        <td>${tripDistanceTotal.toFixed(2)} km</td>
                        <td>${maxSpd} kph</td>
                        <td>${avgSpd} kph</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${i}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }


        function createGeofenceReportForBus(stopLocations, stopLocationsForBus) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">${currentReportType} for ${name} IMEI: ${imei}</h2>
                <table border="1">
                <thead>
                <tr>
                    <th>Trip No</th>
                    <th>Location</th>
                    <th>Address</th>
                    <th>Trip Start Time</th>
                    <th>Trip End Time</th>
                    <th>Trip Duration</th>
                    <th>Trip Distance</th>
                    <th>Total Distance</th>
                    <th>Max Speed</th>
                    <th>Average Speed</th>
                </tr>
                </thead>
                <tbody>
            `;

            const geofenceDistance = 1000; // 200 meters
            let geofenceLocations = [];
            let filteredStopLocations = [];

            for (let i = 0; i < stopLocations.length; i++) {
                const { latitude, longitude, stopEndTime } = stopLocations[i];
                let hasGeofence = false;

                // Check if the current stop is within any existing geofence
                for (let j = 0; j < geofenceLocations.length; j++) {
                    const { latitude: geofenceLat, longitude: geofenceLng } = geofenceLocations[j];
                    const stopDistance = Math.sqrt(Math.pow(parseFloat(latitude) - geofenceLat, 2) + Math.pow(parseFloat(longitude) - geofenceLng, 2)) * 111 * 1000;
                    if (stopDistance < geofenceDistance) {
                        // Update the geofence location to the average of the current stop and the geofence center
                        geofenceLocations[j] = {
                            latitude: (parseFloat(latitude) + geofenceLat) / 2,
                            longitude: (parseFloat(longitude) + geofenceLng) / 2,
                            visits: geofenceLocations[j].visits + 1
                        };
                        hasGeofence = true;
                        break;
                    }
                }

                // If the stop is not within any existing geofence, create a new geofence
                if (!hasGeofence) {
                    geofenceLocations.push({ latitude: parseFloat(latitude), longitude: parseFloat(longitude), visits: 1 });
                }
            }

            // Sort geofences by the number of visits in descending order and select the top 2
            let topGeofenceLocations = geofenceLocations.sort((a, b) => b.visits - a.visits).slice(0, 2);
            console.log('Top geofence locations:', topGeofenceLocations);

            // Filter stops that are within the geofence distance and must exit and re-enter
            const isInsideGeofence = [];
            for (let i = 0; i < stopLocationsForBus.length; i++) {
                const { latitude, longitude, stopStartTime, stopEndTime } = stopLocationsForBus[i];

                for (let j = 0; j < topGeofenceLocations.length; j++) {
                    const { latitude: geofenceLat, longitude: geofenceLng } = topGeofenceLocations[j];
                    const stopDistance = Math.sqrt(Math.pow(latitude - geofenceLat, 2) + Math.pow(longitude - geofenceLng, 2)) * 111 * 1000;

                    if (stopDistance < geofenceDistance) {
                        isInsideGeofence[j] = true;
                        //console.log(latitude, longitude, stopStartTime, stopEndTime, 'is inside geofence');
                    } else {
                        if (isInsideGeofence[j]) {
                            isInsideGeofence[j] = false;
                            //console.log(latitude, longitude, stopStartTime, stopEndTime, 'is outside geofence');
                            filteredStopLocations.push(stopLocationsForBus[i - 1]);
                        }
                    }
                }
            }

            //console.log(filteredStopLocations);


            // Create table rows
            let tripDistanceTotal = 0;
            for (let i = 0; i < filteredStopLocations.length - 1; i++) {
                const { latitude, longitude, stopEndTime, lastAccCode } = filteredStopLocations[i];
                const { stopStartTime, distanceFromLastStop, maxSpeed, averageSpeed } = filteredStopLocations[i + 1];

                // Format tripStartTime, tripEndTime and tripDuration
                const tripStartTimeFormatted = new Date(stopEndTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripEndTimeFormatted = new Date(stopStartTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripDuration = (new Date(stopStartTime) - new Date(stopEndTime)) / 60000; // duration in minutes
                const hours = Math.floor(tripDuration / 60);
                const minutes = Math.round(tripDuration % 60);
                const tripDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${Math.round(tripDuration)} min`;
                const tripDistance = `${(distanceFromLastStop / 1000).toFixed(2)} km`;
                tripDistanceTotal += distanceFromLastStop / 1000;
                const maxSpd = maxSpeed.toFixed(0);
                const avgSpd = averageSpeed.toFixed(0);

                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                        <td>${i + 1}</td>
                        <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                        <td id="address-${i}">${addressPlaceholder}</td>
                        <td>${tripStartTimeFormatted}</td>
                        <td>${tripEndTimeFormatted}</td>
                        <td>${tripDurationFormatted}</td>
                        <td>${tripDistance}</td>
                        <td>${tripDistanceTotal.toFixed(2)} km</td>
                        <td>${maxSpd} kph</td>
                        <td>${avgSpd} kph</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${i}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }

        function createFuelReport(stopLocations) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">${currentReportType} for ${name} IMEI: ${imei}</h2>
                <table border="1">
                <thead>
                <tr>
                    <th>Trip No</th>
                    <th>Fuel Level</th>
                    <th>Fuel Refilled</th>
                    <th>Fuel Consumed</th>
                    <th>Fuel Efficiency</th>
                    <th>Location</th>
                    <th>Address</th>
                    <th>Trip Start Time</th>
                    <th>Trip End Time</th>
                    <th>Trip Duration</th>
                    <th>Trip Distance</th>
                    <th>Average Speed</th>
                </tr>
                </thead>
                <tbody>
            `;

            let tripDistanceTotal = 0;
            let totalFuelRefilled = 0;
            let totalFuelConsumed = 0;
            let totalTripDuration = 0;

            for (let i = 0; i < stopLocations.length - 1; i++) {
                const { latitude, longitude, stopEndTime, lastAccCode, startFuelLevel: startFuelLevel1, endFuelLevel: endFuelLevel1 } = stopLocations[i];
                const { stopStartTime, distanceFromLastStop, maxSpeed, averageSpeed, startFuelLevel: startFuelLevel2, endFuelLevel: endFuelLevel2 } = stopLocations[i + 1];

                // Format tripStartTime, tripEndTime and tripDuration
                const tripStartTimeFormatted = new Date(stopEndTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripEndTimeFormatted = new Date(stopStartTime.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const tripDuration = (new Date(stopStartTime) - new Date(stopEndTime)) / 60000; // duration in minutes
                totalTripDuration += typeof tripDuration === 'number' ? tripDuration : 0;

                const hours = Math.floor(tripDuration / 60);
                const minutes = Math.round(tripDuration % 60);
                const tripDurationFormatted = hours >= 1 ? `${hours} hr ${minutes} min` : `${Math.round(tripDuration)} min`;
                const tripDistance = `${(distanceFromLastStop / 1000).toFixed(2)} km`;
                tripDistanceTotal += distanceFromLastStop / 1000;
                const maxSpd = maxSpeed.toFixed(0);
                const avgSpd = averageSpeed.toFixed(0);

                const fuelRefill = endFuelLevel1 - startFuelLevel1;
                const fuelRefilled = fuelRefill > 10 ? fuelRefill : 0;
                totalFuelRefilled += fuelRefill;

                const fuelConsumed = (endFuelLevel1 - startFuelLevel2);
                totalFuelConsumed += fuelConsumed;

                const fuelEfficiency = (distanceFromLastStop / 1000) / fuelConsumed;

                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                        <td>${i + 1}</td>
                        <td>${endFuelLevel1.toFixed(0)} - ${startFuelLevel2.toFixed(0)}</td>
                        <td>${fuelRefilled.toFixed(2)}</td>
                        <td>${fuelConsumed.toFixed(2)}</td>
                        <td>${fuelEfficiency.toFixed(2)}</td>
                        <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                        <td id="address-${i}">${addressPlaceholder}</td>
                        <td>${tripStartTimeFormatted}</td>
                        <td>${tripEndTimeFormatted}</td>
                        <td>${tripDurationFormatted}</td>
                        <td>${tripDistance}</td>
                        <td>${avgSpd} kph</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${i}`).innerText = address;
                });
            }

            const totalFuelEfficiency = totalFuelConsumed / (tripDistanceTotal / 1000);

            const hours = Math.floor(totalTripDuration / 60);
            const minutes = Math.round(totalTripDuration % 60);
            const totalTripDurationFormatted = `${hours} hr ${minutes} min`;

            tableHtml += `
                <tr>
                    <td colspan="2">Total</td>
                    <td>${totalFuelRefilled.toFixed(2)} L</td>
                    <td>${totalFuelConsumed.toFixed(2)} L</td>
                    <td>${totalFuelEfficiency.toFixed(2)} km/L</td>
                    <td colspan="4"></td>
                    <td>${totalTripDurationFormatted}</td>
                    <td>${tripDistanceTotal.toFixed(2)} km</td>
                </tr>
            `;

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }


        async function createAlarmReport(data) {
            // Create HTML table
            let tableHtml = `
                <h2 class="table-title">${currentReportType} for ${name} IMEI: ${imei}</h2>
                <table border="1">
                <thead>
                <tr>
                <th>No</th>
                <th>Alarm</th>
                <th>GPS Timestamp</th>
                <th>Location</th>
                <th>Address</th>
                <th>Satellite</th>
                <th>Speed</th>
                <th>Ignition</th>
                <th>In Status</th>
                <th>Out Status</th>
                <th>Ext Battery</th>
                <th>Int Battery</th>
                </tr>
                </thead>
                <tbody>
            `;

            for (const [index, records] of data.entries()) {
                const { gpsTimestamp, latitude, longitude, speed, satelliteCount, inStatus, accCode, outStatus, extBat, intBat, iButton, alarmCode } = records;

                // Format GPS timestamp
                const gpsTimestampFormatted = new Date(gpsTimestamp.replace(" ", "T") + "Z").toLocaleString('en-US', options).replace(/\//g, '-').replace(',', '');
                const googleMapsLink = `https://www.google.com/maps?q=${latitude},${longitude}`;

                // Add a placeholder for the address
                const addressPlaceholder = `Loading...`;

                tableHtml += `
                    <tr>
                    <td>${index + 1}</td>
                    <td>${alarmCode}</td>
                    <td>${gpsTimestampFormatted}</td>
                    <td><a href="${googleMapsLink}" target="_blank">${latitude}, ${longitude}</a></td>
                    <td id="address-${index}">${addressPlaceholder}</td>
                    <td>${satelliteCount}</td>
                    <td>${speed}</td>
                    <td>${accCode}</td>
                    <td>${inStatus}</td>
                    <td>${outStatus}</td>
                    <td>${extBat}</td>
                    <td>${intBat}</td>
                    </tr>
                `;

                // Fetch the address asynchronously and update the table
                getAddress(latitude, longitude).then(address => {
                    document.getElementById(`address-${index}`).innerText = address;
                });
            }

            tableHtml += `</tbody></table>`;
            return tableHtml;
        }

        // SUPPORT FUNCTIONS

        async function getAddress(lat, lng) {
            const url = `http://dev.inavcloud.com:3100/api/reverse-geocode?lat=${lat}&lng=${lng}`;

            try {
                const response = await fetch(url);
                const data = await response.json();

                if (data.address) {
                    return data.address;
                } else {
                    console.log('No results found');
                    return null;
                }
            } catch (error) {
                console.error('Error:', error);
                return null;
            }
        }

        function downloadCSV() {
            const table = document.getElementById('reportContent');
            const rows = table.querySelectorAll('tr');
            let csvContent = '';

            rows.forEach(row => {
                const cells = row.querySelectorAll('th, td');
                const rowContent = Array.from(cells).map(cell => `"${cell.innerText.replace(/"/g, '""')}"`).join(',');
                csvContent += rowContent + '\n';
            });

            // Create a Blob object with the CSV data
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a link element, set its href to the Blob URL, and trigger a download
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            const startUTCInManila = new Date(startUTC).toLocaleString('en-US', options).replace(/, /g, '_');
            const endUTCInManila = new Date(endUTC).toLocaleString('en-US', options).replace(/, /g, '_');
            link.setAttribute('download', `${currentReportType}_${name}_${imei}_${startUTCInManila}_${endUTCInManila}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

    </script>

</body>

</html>