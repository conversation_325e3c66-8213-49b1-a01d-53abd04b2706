<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iNav Firmware Update</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #F4F8FA;
            color: #061238;
            text-align: center;
        }

        .content {
            height: 40px;
            padding: 5px;
            align-items: center;
            justify-content: space-between;
        }

        .content h1 {
            color: #0A4951;
            font-size: 15px;
            padding: 10px;
        }

        .row {
            display: flex;
            height: calc(100% - 60px);
            border-top: 1px solid #DAE6EF;
        }

        .sidebar-container {
            background-color: #FFF6E6;
            width: 30%;
            height: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: width 0.3s;
        }

        .form-container {
            flex: 0 0 10%;
            width: 100%;
            background-color: #F9FBFD
        }

        .list-container {
            flex: 1 1 90%;
            width: 100%;
            overflow-y: auto;
        }

        .count-container {
            color: #061238;
            display: flex;
            height: 0%;
            justify-content: space-between;
            padding: 10px 5px;
            margin-bottom: 25px;
            font-size: 11px;
            align-items: bottom-center;
        }

        .marker-list {
            background-color: #F9FBFD;
            border-top: 1px solid #DAE6EF;
            overflow-y: auto;
            font-size: 13px;
            padding: 10px;
            text-align: left;
            line-height: 1.5;
            list-style-type: decimal;
            flex-grow: 1;
            width: 100%;
            margin: 0;
        }

        .marker-list-item {
            background-color: white;
            padding: 5px;
            border-bottom: 1px solid #DAE6EF;
            cursor: pointer;
        }

        .marker-list-item:hover {
            background-color: #F9FBFD;
            /* A slightly lighter blue on hover */
        }

        /* Mobile layout */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 0;
                display: none;
            }

            .toggle-button {
                display: block;
            }

            .settings-button {
                display: block;
            }
        }

        #csvForm {
            padding: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-grow: 1;
            vertical-align: top;
        }

        #csvForm label {
            color: #061238;
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            text-align: left;
            font-size: 13px;
        }

        #csvInput {
            width: 100%;
            padding: 10px;
            border: 1px solid #DAE6EF;
            border-radius: 4px;
            box-sizing: border-box;
            height: 30px;
            resize: vertical;
            font-size: 12px;
        }

        .button-container {
            display: flex;
            gap: 10px;
        }

        #csvForm button {
            color: #F9FBFD;
            background-color: #3BB3C3;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }

        #csvForm button:hover {
            background-color: #156E7F;
            color: white;
        }

        #deleteDataButton:hover {
            background-color: #156E7F;
            color: white;
        }

        .material-icons-outlined {
            vertical-align: middle;
        }

        .toggle-button {
            position: absolute;
            top: 5px;
            left: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .toggle-button:hover {
            background-color: #DAE6EF;
        }

        .settings-button {
            position: absolute;
            top: 5px;
            right: 50px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .settings-button:hover {
            background-color: #DAE6EF;
        }

        .dropdown {
            display: none;
            position: absolute;
            right: 20px;
            top: 60px;
            background-color: #F4F8FA;
            border: 1px solid #DAE6EF;
            z-index: 1200;
            padding: 20px;
            text-align: left;
            font-size: 13px;
        }

        .dropdown input[type="checkbox"] {
            margin-right: 5px;
            margin-bottom: 10px;
        }

        .dropdown input[type="checkbox"]:checked {
            accent-color: #156E7F;
        }
    </style>
</head>

<body>
    <div class="content">
        <h1>iNav Firmware Update</h1>
        <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">
            <i class="material-icons-outlined">menu</i>
        </button>
        <button class="settings-button" id="settingsButton">
            <i class="material-icons-outlined">settings</i>
        </button>
    </div>

    <div class="row">
        <div sidebar class="sidebar-container" id="sidebar">
            <div class="row form-container">
                <form id="csvForm">
                    <label for="csvInput">Enter CSV (IMEI,Name):</label>
                    <textarea id="csvInput" rows="10" cols="40"></textarea><br>
                    <div class="button-container">
                        <button type="submit">Submit</button>
                        <button type="button" id="deleteDataButton">Delete</button>
                    </div>
                </form>
            </div>

            <!-- Count Container -->
            <div class="row count-container">
                <div id="count">
                    <b>All:</b></All:> <span id="allCount"></span>
                    | <b>Success:</b> <span id="onlineCount"></span>
                    | <b>Failed:</b> <span id="offlineCount"></span>
                    | <b>Scheduled:</b> <span id="delayCount"></span>
                </div>
            </div>

            <!-- Marker List -->
            <div class="row list-container">
                <div id="markerList" class="marker-list"></div>
            </div>
        </div>


        <div class="map-container">
            <div id="map" style="height: 100%; width: 100%;"></div>
            <!-- Spinner -->
            <div id="spinner" class="spinner" style="display: none;"></div>
        </div>
    </div>

    <!-- Dropdown Menu -->
    <div class="dropdown" id="dropdown">
        <div>Select Project APIs</div>
        <br>
        <label>
            <input type="checkbox" id="selectAll"> All
        </label>
        <br>
        <label>
            <input type="checkbox" id="mpuv" class="port-checkbox" value="3003"> MPUV
        </label>
        <br>
        <label>
            <input type="checkbox" id="unilever" class="port-checkbox" value="3004"> Unilever
        </label>
        <br>
        <label>
            <input type="checkbox" id="loconav" class="port-checkbox" value="3005"> Loconav
        </label>
        <br>
        <label>
            <input type="checkbox" id="2Go" class="port-checkbox" value="3006"> 2Go
        </label>
    </div>

    <script>
        let options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
            timeZone: 'Asia/Manila'
        };


        // Toggle the sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mapColumn = document.querySelector('.map-container');
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '50%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '50%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            } else {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '22.95%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '77.05%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            }
        }

        // Array to store selected ports
        let selectedPorts = [];
        let accumulatedUserData = {};

        const settingsBtn = document.querySelector('.settings-button');
        const dropdown = document.getElementById('dropdown');

        // Toggle the dropdown menu
        document.querySelector('.settings-button').addEventListener('click', () => {
            const dropdown = document.getElementById('dropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            const isClickInside = dropdown.contains(event.target) || settingsBtn.contains(event.target);
            if (!isClickInside) {
                dropdown.style.display = 'none';
            }
        });

        // Handle select all
        document.getElementById('selectAll').addEventListener('change', function () {
            const checkboxes = document.querySelectorAll('.port-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedPorts();
        });

        // Handle individual checkbox change
        document.querySelectorAll('.port-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                document.getElementById('selectAll').checked = false; // Deselect "All" if individual boxes are changed
                updateSelectedPorts();
            });
        });

        // Update selected ports in array
        async function updateSelectedPorts() {
            selectedPorts = [];
            const checkboxes = document.querySelectorAll('.port-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedPorts.push(checkbox.value);
            });
            // Save selected ports to localStorage
            localStorage.setItem('selectedPorts', JSON.stringify(selectedPorts));
            //console.log('Selected ports:', selectedPorts);
            await getAccumulatedUserData(selectedPorts);
        }

        // Load selected ports from localStorage when the page loads
        window.onload = async function () {
            const savedPorts = JSON.parse(localStorage.getItem('selectedPorts')) || [];
            const checkboxes = document.querySelectorAll('.port-checkbox');
            checkboxes.forEach(checkbox => {
                if (savedPorts.includes(checkbox.value)) {
                    checkbox.checked = true;
                    selectedPorts.push(checkbox.value);
                }
            });
            // Check if all are selected
            document.getElementById('selectAll').checked = checkboxes.length === savedPorts.length;
            //console.log('Selected ports:', selectedPorts);
        };

        document.addEventListener('DOMContentLoaded', () => {

            const savedCsvData = localStorage.getItem('csvData');
            if (savedCsvData) {
                document.getElementById('csvInput').value = savedCsvData;
                const csvLines = savedCsvData.split('\n');
                const imeiData = csvLines.map(line => {
                    let [imei, name] = line.split(',');
                    name = name ? name.trim() : '';
                    return { imei, name };
                });
                plotLocations(imeiData);
            }

            document.getElementById('csvForm').addEventListener('submit', function (event) {
                event.preventDefault();

                const csvInput = document.getElementById('csvInput').value.trim();
                localStorage.setItem('csvData', csvInput);

                const csvLines = csvInput.split('\n');
                const imeiData = csvLines.map(line => {
                    let [imei, name] = line.split(',');
                    name = name ? name.trim() : '';
                    return { imei: imei.trim(), name: name.trim() };
                });

                location.reload();
            });

            document.getElementById('deleteDataButton').addEventListener('click', handleDeleteDataClick);

            let deleteClickCount = 0;
            let clickTimer;

            function handleDeleteDataClick() {
                deleteClickCount++;

                clearTimeout(clickTimer);
                clickTimer = setTimeout(async () => {
                    if (deleteClickCount === 3) {
                        plotAllLocations(); // Re-plot all locations after deletion
                    } else {
                        deleteData(); // Just delete the data
                    }
                    deleteClickCount = 0;
                }, 500);
            }

            function deleteData() {
                localStorage.removeItem('csvData');
                localStorage.removeItem('accumulatedUserData');
                document.getElementById('csvInput').value = '';
                location.reload();
            }
        });

    </script>
</body>

</html>
