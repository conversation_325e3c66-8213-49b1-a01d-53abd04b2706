// <PERSON>
// Sep 22, 2024
// Singleton for sqlite database

// TCP server for device connectiions
// |------> <PERSON>i handler -----> destination server
// |------> Teltonika handler -----> destination server
// |------> Amwell handler -----> destination server
// |------> iStartek handler -----> destination server
// |------> api.js <------> webhost.js <------> index.html
// |------> this db.js

// npm install sqlite3
// npm install cors
// npm install node-fetch@2
// sudo resize2fs /dev/sda
// sqlite3 /mnt/volume_sgp1_01/reverse_geocode.db -header -column "SELECT * FROM reverse_geocode LIMIT 100;"

// http://dev.inavcloud.com:3100/api/reverse-geocode?lat=14.584926&lng=120.978701
// https://api.opencagedata.com/geocode/v1/json?q=14.584926+120.978701&key=031b3fe673e747deab496415d4332359

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const express = require('express');
const fetch = require('node-fetch');
const cors = require('cors');

const DB_NAME = 'reverse_geocode.db'; // Replace with your actual database name
//const dbPath = path.resolve('/mnt/volume_sgp1_01', DB_NAME);
const dbPath = path.resolve(__dirname, 'db', DB_NAME);
//const dbPath = path.resolve(__dirname, DB_NAME);
let dbInstance = null;

function getDbInstance() {
    if (!dbInstance) {
        dbInstance = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
            if (err) {
                console.error('Error opening database', err.message);
            } else {
                console.log(`Connected to the SQLite ${DB_NAME} database.`);
                dbInstance.serialize(() => {
                    dbInstance.run(`PRAGMA journal_mode = WAL`);

                    // Create table to store reverse geocode results
                    dbInstance.run(`CREATE TABLE IF NOT EXISTS reverse_geocode (
                        lat TEXT,
                        lng TEXT,
                        address TEXT,
                        PRIMARY KEY (lat, lng)
                    )`, (err) => {
                        if (err) {
                            console.error('Error creating reverse_geocode table', err.message);
                        }
                    });
                });
            }
        });
    }
    return dbInstance;
}

const db = getDbInstance();
const app = express();
app.use(express.json());
app.use(cors()); // Enable CORS for all routes

app.get('/api/reverse-geocode', async (req, res) => {
    const { lat, lng } = req.query;

    if (!lat || !lng) {
        return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    db.get('SELECT address FROM reverse_geocode WHERE lat = ? AND lng = ?', [lat, lng], async (err, row) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }

        if (row) {
            return res.json({ address: row.address });
        } else {
            const apiKey = '031b3fe673e747deab496415d4332359'; // Replace with your OpenCage API key
            const url = `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lng}&key=${apiKey}`;

            try {
                const response = await fetch(url);
                const data = await response.json();

                if (data.results && data.results.length > 0) {
                    const address = data.results[0].formatted;
                    db.run('INSERT INTO reverse_geocode (lat, lng, address) VALUES (?, ?, ?)', [lat, lng, address], (err) => {
                        if (err) {
                            return res.status(500).json({ error: 'Database error' });
                        }
                        return res.json({ address });
                    });
                } else {
                    return res.status(404).json({ error: 'No results found' });
                }
            } catch (error) {
                return res.status(500).json({ error: 'API error' });
            }
        }
    });
});

const PORT = process.env.PORT || 3100;
app.listen(PORT, () => {
    console.log(`Reverse geocode server is running on port ${PORT}`);
});