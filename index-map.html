<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iNav Fleet</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #F4F8FA;
            color: #061238;
            text-align: center;
        }

        .content {
            height: 40px;
            padding: 5px;
            align-items: center;
            justify-content: space-between;
        }

        .content h1 {
            color: #0A4951;
            font-size: 15px;
            padding: 10px;
        }

        .row {
            display: flex;
            height: calc(100% - 60px);
            border-top: 1px solid #DAE6EF;
        }

        .sidebar-container {
            background-color: #FFF6E6;
            width: 30%;
            height: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: width 0.3s;
        }

        .form-container {
            flex: 0 0 10%;
            width: 100%;
            background-color: #F9FBFD
        }

        .list-container {
            flex: 1 1 90%;
            width: 100%;
            overflow-y: auto;
        }

        .count-container {
            color: #061238;
            display: flex;
            height: 0%;
            justify-content: space-between;
            padding: 10px 5px;
            margin-bottom: 25px;
            font-size: 11px;
            align-items: bottom-center;
        }

        .marker-list {
            background-color: #F9FBFD;
            border-top: 1px solid #DAE6EF;
            overflow-y: auto;
            font-size: 13px;
            padding: 10px;
            text-align: left;
            line-height: 1.5;
            list-style-type: decimal;
            flex-grow: 1;
            width: 100%;
            margin: 0;
        }

        .marker-list-item {
            background-color: white;
            padding: 5px;
            border-bottom: 1px solid #DAE6EF;
            cursor: pointer;
        }

        .marker-list-item:hover {
            background-color: #F9FBFD;
            /* A slightly lighter blue on hover */
        }


        .marker-list-item .message-details {
            display: none;
            padding-left: 10px;
            padding: 7px;
            color: #061238;
            font-size: 12px;
            border-left: 5px solid #B2C7D7;
            line-height: 1.2;
        }

        .map-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100%;
            transition: width 0.2s;
        }

        /* Mobile layout */
        @media (max-width: 768px) {
            .sidebar-container {
                width: 0;
                display: none;
            }

            .map-container {
                width: 100%;
            }

            .toggle-button {
                display: block;
            }

            .summarize-button {
                display: block;
            }

            .settings-button {
                display: block;
            }
        }

        #csvForm {
            padding: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-grow: 1;
            vertical-align: top;
        }

        #csvForm label {
            color: #061238;
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            text-align: left;
            font-size: 13px;
        }

        #csvInput {
            width: 100%;
            padding: 10px;
            border: 1px solid #DAE6EF;
            border-radius: 4px;
            box-sizing: border-box;
            height: 30px;
            resize: vertical;
            font-size: 12px;
        }

        .button-container {
            display: flex;
            gap: 10px;
        }

        #csvForm button {
            color: #F9FBFD;
            background-color: #3BB3C3;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            flex: 1;
        }

        #csvForm button:hover {
            background-color: #156E7F;
            color: white;
        }

        #deleteDataButton:hover {
            background-color: #156E7F;
            color: white;
        }

        #map {
            height: 100%;
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex-grow: 1;
        }

        .material-icons-outlined {
            vertical-align: middle;
        }

        .toggle-button {
            position: absolute;
            top: 5px;
            left: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .toggle-button:hover {
            background-color: #DAE6EF;
        }

        .summarize-button {
            position: absolute;
            top: 5px;
            right: 90px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .summarize-button:hover {
            background-color: #DAE6EF;
        }

        .settings-button {
            position: absolute;
            top: 5px;
            right: 50px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .settings-button:hover {
            background-color: #DAE6EF;
        }

        .user-info {
            position: absolute;
            top: 5px;
            right: 10px;
            background-color: #F4F8FA;
            color: #156E7F;
            border: none;
            padding: 5px;
            border-radius: 1px;
            cursor: pointer;
            z-index: 1000;
        }

        .user-info:hover {
            background-color: #DAE6EF;
        }

        .dropdown {
            display: none;
            position: absolute;
            right: 20px;
            top: 60px;
            background-color: #F4F8FA;
            border: 1px solid #DAE6EF;
            z-index: 1200;
            padding: 20px;
            text-align: left;
            font-size: 13px;
        }

        .dropdown input[type="checkbox"] {
            margin-right: 5px;
            margin-bottom: 10px;
        }

        .dropdown input[type="checkbox"]:checked {
            accent-color: #156E7F;
        }

        .open-button .material-icons-outlined {
            font-size: 15px;
            color: #566B88;
            transition: color 0.3s ease;
            display: inline-block;
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 15;
        }

        .open-button:hover .material-icons-outlined {
            background-color: #DAE6EF;
            /* Change icon color on hover */
        }

        .replay-button .material-icons-outlined {
            font-size: 15px;
            color: #566B88;
            transition: color 0.3s ease;
            display: inline-block;
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 15
        }

        .replay-button:hover .material-icons-outlined {
            background-color: #DAE6EF;
            /* Change icon color on hover */
        }

        .reports-button .material-icons-outlined {
            font-size: 15px;
            color: #566B88;
            transition: color 0.3s ease;
            display: inline-block;
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 15
        }

        .reports-button:hover .material-icons-outlined {
            background-color: #DAE6EF;
            /* Change icon color on hover */
        }

        .fuel-button .material-icons-outlined {
            font-size: 15px;
            color: #566B88;
            transition: color 0.3s ease;
            display: inline-block;
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 15
        }

        .fuel-button:hover .material-icons-outlined {
            background-color: #DAE6EF;
            /* Change icon color on hover */
        }

        .temperature-button .material-icons-outlined {
            font-size: 15px;
            color: #566B88;
            transition: color 0.3s ease;
            display: inline-block;
            font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 15
        }

        .temperature-button:hover .material-icons-outlined {
            background-color: #DAE6EF;
            /* Change icon color on hover */
        }

        .car-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 12px;
            text-align: center;
            color: #000;
            cursor: pointer;
        }

        .car-icon img {
            width: 28px;
            height: 28px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .car-icon-text {
            position: absolute;
            bottom: -5px;
            width: 100%;
            text-align: center;
            font-size: 10px;
            font-weight: bold;
        }

        .rotate-icon {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            transform-origin: center;
        }

        #fuelChartContainer {
            display: none;
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 10px;
            z-index: 1200;
            width: 550px;
            height: 285px;
        }

        #temperatureChartContainer {
            display: none;
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 10px;
            z-index: 1200;
            width: 550px;
            height: 285px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 40%;
            overflow: scroll;
            background-color: rgb(0, 0, 0);
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 1% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            z-index: 1110;
            font-size: 14px;
            line-height: 1.5;
            text-align: left;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        /* Form Styles */
        #commandForm {
            display: flex;
            flex-direction: column;
        }

        #commandForm label {
            margin-bottom: 5px;
            font-weight: bold;
        }

        #commandForm input[type="text"] {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #DAE6EF;
            border-radius: 4px;
            font-size: 13px;
            width: 70%;
        }

        .submit-button {
            background-color: #3BB3C3;
            /* Green */
            color: #F9FBFD;
            padding: 5px 15px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.3s;
        }

        .submit-button:hover {
            background-color: #156E7F;
            /* Darker green */
        }

        /* Response Styles */
        .responseCommand {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f1f1f1;
            border: 1px solid #DAE6EF;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
            max-width: 100%;
            box-sizing: border-box;
            font-size: 13px;
        }

        .responseCommand.success {
            background-color: #d4edda;
            color: #155724;
        }

        .responseCommand.error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .responseRawData {
            height: 200px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            border-radius: 4px;
            background-color: #f1f1f1;
            border: 1px solid #DAE6EF;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
            max-width: 100%;
            box-sizing: border-box;
            font-family: monospace;
            font-size: 14px;
        }

        .spinner {
            border: 4px solid #F9FBFD;
            border-top: 4px solid #3BB3C3;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 17%;
            right: 1.2%;
            transform: translate(-50%, -50%);
            z-index: 1100;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.css" />
</head>

<body>
    <div class="content">
        <h1>iNav Fleet Monitoring</h1>
        <button class="toggle-button" id="toggleButton" onclick="toggleSidebar()">
            <i class="material-icons-outlined">menu</i>
        </button>
        <button class="summarize-button" id="summarizeButton">
            <i class="material-icons-outlined">summarize</i>
        </button>
        <button class="settings-button" id="settingsButton">
            <i class="material-icons-outlined">settings</i>
        </button>
        <button class="user-info" id="userInfo" onclick="login()">
            <i class="material-icons-outlined">account_circle</i>
        </button>
    </div>

    <div class="row">
        <div sidebar class="sidebar-container" id="sidebar">
            <div class="row form-container">
                <form id="csvForm">
                    <label for="csvInput">Enter CSV (IMEI,Name):</label>
                    <textarea id="csvInput" rows="10" cols="40"></textarea><br>
                    <div class="button-container">
                        <button type="submit">Submit</button>
                        <button type="button" id="deleteDataButton">Delete</button>
                    </div>
                </form>
            </div>

            <!-- Count Container -->
            <div class="row count-container">
                <div id="count">
                    <b>All:</b></All:> <span id="allCount"></span>
                    | <b>Online:</b> <span id="onlineCount"></span>
                    | <b>Offline:</b> <span id="offlineCount"></span>
                    | <b>Delay:</b> <span id="delayCount"></span>
                    | <b>Running:</b> <span id="runningCount"></span>
                    | <b>Idling:</b> <span id="idlingCount"></span>
                </div>
            </div>

            <!-- Marker List -->
            <div class="row list-container">
                <div id="markerList" class="marker-list"></div>
            </div>
        </div>


        <div class="map-container">
            <div id="map" style="height: 100%; width: 100%;"></div>
            <!-- Spinner -->
            <div id="spinner" class="spinner" style="display: none;"></div>
        </div>
    </div>


    <!-- The Modal -->
    <div id="commandModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h4>Send Command</h4>
            <br>
            <form id="commandForm" class="form-inline">
                <input type="hidden" id="modalImei">
                <div class="form-group">
                    <label for="modalCommand" class="form-label">Command:</label>
                    <input type="text" id="modalCommand" name="command" required class="form-input">
                    <button type="submit" class="submit-button">Send</button>
                </div>
            </form>
            <div id="commandResponse" class="responseCommand"></div>
            <br>
            <p>Received Data:</p>
            <div id="rawDataResponse" class="responseRawData"></div>
        </div>
    </div>

    <!-- Login dropdown -->


    <!-- Dropdown Menu -->
    <div class="dropdown" id="dropdown">
        <div>Select Project APIs</div>
        <br>
        <label>
            <input type="checkbox" id="selectAll"> All
        </label>
        <br>
        <label>
            <input type="checkbox" id="mpuv" class="port-checkbox" value="3003"> MPUV
        </label>
        <br>
        <label>
            <input type="checkbox" id="unilever" class="port-checkbox" value="3004"> Unilever
        </label>
        <br>
        <label>
            <input type="checkbox" id="loconav" class="port-checkbox" value="3005"> Loconav
        </label>
        <br>
        <label>
            <input type="checkbox" id="2Go" class="port-checkbox" value="3006"> 2Go
        </label>
    </div>


    <!-- Fuel Chart Container -->
    <div id="fuelChartContainer">
        <canvas id="fuelChart"></canvas>
    </div>

    <!-- Temperature Chart Container -->
    <div id="temperatureChartContainer">
        <canvas id="temperatureChart"></canvas>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mark.js/8.11.1/mark.min.js"></script>
    <!-- Chart.js 4.4.0 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/hammerjs@latest"></script>
    <script src="https://apis.google.com/js/api.js"></script>

    <script>
        let options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
            timeZone: 'Asia/Manila'
        };


        // Toggle the sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mapColumn = document.querySelector('.map-container');
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '50%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '50%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            } else {
                if (sidebar.style.width === '0px' || sidebar.style.display === 'none') {
                    sidebar.style.width = '22.95%';
                    sidebar.style.display = 'flex';
                    mapColumn.style.width = '77.05%';
                } else {
                    sidebar.style.width = '0';
                    sidebar.style.display = 'none';
                    mapColumn.style.width = '100%';
                }
            }
        }

        // Array to store selected ports
        let selectedPorts = [];
        let accumulatedUserData = {};

        function csvToJson(csv) {
            const lines = csv.split('\n');
            const result = [];

            // Regex to match CSV fields, accounting for quoted strings and empty fields
            const csvRegex = /(?:^|,)(?:"([^"]*)"|([^,]*))/g;

            for (let i = 1; i < lines.length; i++) {
                const matches = [...lines[i].matchAll(csvRegex)];
                const currentline = matches.map(match => (match[1] || match[2] || '').trim()); // Handle quoted and unquoted fields

                const obj = {
                    name: currentline[0] || "",
                    imei: currentline[1] || "",
                    brand: currentline[2] || "", // Ensure correct alignment for brand
                    endSub: currentline[3] || "", // Ensure correct alignment for End of Subscription
                    sim: currentline[4] || "", // Ensure correct alignment for SIM
                    calibration: currentline[5] || "", // Properly place Calibration
                };

                result.push(obj);
            }
            return result;
        }

        async function getNameData(port) {
            const csvMpuv = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRDeqKANis-4J-CZZdymhq3xwf_K73Vm-7w35tWZyJ4o_uZQvJbymAXHjgdSZJY_L4TmLf_7PR3diBo/pub?gid=1336316297&single=true&output=csv';
            const csvUniliver = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRDeqKANis-4J-CZZdymhq3xwf_K73Vm-7w35tWZyJ4o_uZQvJbymAXHjgdSZJY_L4TmLf_7PR3diBo/pub?gid=0&single=true&output=csv';
            const csvLoconav = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRDeqKANis-4J-CZZdymhq3xwf_K73Vm-7w35tWZyJ4o_uZQvJbymAXHjgdSZJY_L4TmLf_7PR3diBo/pub?gid=864249201&single=true&output=csv';
            const csv2go = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRDeqKANis-4J-CZZdymhq3xwf_K73Vm-7w35tWZyJ4o_uZQvJbymAXHjgdSZJY_L4TmLf_7PR3diBo/pub?gid=1980940907&single=true&output=csv';

            let csvUrl;
            switch (port) {
                case '3003':
                    csvUrl = csvMpuv;
                    break;
                case '3004':
                    csvUrl = csvUniliver;
                    break;
                case '3005':
                    csvUrl = csvLoconav;
                    break;
                case '3006':
                    csvUrl = csv2go;
                    break;
                default:
                    throw new Error('Invalid port selected');
            }

            // Return the data after it has been fetched and parsed
            return fetch(csvUrl)
                .then(response => response.text())
                .then(data => {
                    const json = csvToJson(data);
                    return json;  // Important: Return the JSON data
                })
                .catch(error => {
                    console.error('Error fetching the CSV data:', error);
                    throw error;
                });
        }

        async function getAccumulatedUserData(selectedPorts) {
            for (const port of selectedPorts) {
                const nameDataArray = await getNameData(port);

                // Convert the array of {name, imei} to an object where imei is the key
                for (const entry of nameDataArray) {
                    accumulatedUserData[entry.imei] = entry;
                }
            }

            // Store the accumulated data in localStorage
            localStorage.setItem('accumulatedUserData', JSON.stringify(accumulatedUserData));
            console.log('csv data stored in localStorage');
        }

        // Function to load the data from localStorage on a different page
        async function loadAccumulatedUserData(selectedPorts) {
            const storedData = localStorage.getItem('accumulatedUserData');
            if (storedData) {
                accumulatedUserData = JSON.parse(storedData);
                console.log('Data loaded from localStorage');
            } else {
                await getAccumulatedUserData(selectedPorts);
            }
        }

        const settingsBtn = document.querySelector('.settings-button');
        const dropdown = document.getElementById('dropdown');

        // Toggle the dropdown menu
        document.querySelector('.settings-button').addEventListener('click', () => {
            const dropdown = document.getElementById('dropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            const isClickInside = dropdown.contains(event.target) || settingsBtn.contains(event.target);
            if (!isClickInside) {
                dropdown.style.display = 'none';
            }
        });

        // Handle select all
        document.getElementById('selectAll').addEventListener('change', function () {
            const checkboxes = document.querySelectorAll('.port-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedPorts();
        });

        // Handle individual checkbox change
        document.querySelectorAll('.port-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                document.getElementById('selectAll').checked = false; // Deselect "All" if individual boxes are changed
                updateSelectedPorts();
            });
        });

        // Update selected ports in array
        async function updateSelectedPorts() {
            selectedPorts = [];
            const checkboxes = document.querySelectorAll('.port-checkbox:checked');
            checkboxes.forEach(checkbox => {
                selectedPorts.push(checkbox.value);
            });
            // Save selected ports to localStorage
            localStorage.setItem('selectedPorts', JSON.stringify(selectedPorts));
            //console.log('Selected ports:', selectedPorts);
            await getAccumulatedUserData(selectedPorts);
        }

        // Load selected ports from localStorage when the page loads
        window.onload = async function () {
            const savedPorts = JSON.parse(localStorage.getItem('selectedPorts')) || [];
            const checkboxes = document.querySelectorAll('.port-checkbox');
            checkboxes.forEach(checkbox => {
                if (savedPorts.includes(checkbox.value)) {
                    checkbox.checked = true;
                    selectedPorts.push(checkbox.value);
                }
            });
            // Check if all are selected
            document.getElementById('selectAll').checked = checkboxes.length === savedPorts.length;
            //console.log('Selected ports:', selectedPorts);
        };

        // Summarize Button
        document.querySelector('.summarize-button').addEventListener('click', function () {
            window.open(`/summarize?&port=${selectedPorts}`, '_blank');
        });;

        function csvToFuelTable(csv) {
            const values = csv.split(',').map(Number); // Split and convert to numbers
            const fuelTable = [];

            for (let i = 0; i < values.length; i += 2) {
                fuelTable.push({ litres: values[i], sensor: values[i + 1] });
            }

            return fuelTable;
        }

        // Function to calculate fuel level given a sensor reading
        function getFuelLevel(sensorReading, fuelTable) {
            if (fuelTable.length === 0) return sensorReading;

            // Find the two closest points
            let lowerPoint = fuelTable[0];
            let upperPoint = fuelTable[fuelTable.length - 1];

            for (let i = 0; i < fuelTable.length - 1; i++) {
                if (sensorReading >= fuelTable[i].sensor && sensorReading <= fuelTable[i + 1].sensor) {
                    lowerPoint = fuelTable[i];
                    upperPoint = fuelTable[i + 1];
                    break;
                }
            }

            // Perform linear interpolation
            const interpolatedLitres = (lowerPoint.litres +
                ((sensorReading - lowerPoint.sensor) * (upperPoint.litres - lowerPoint.litres)) /
                (upperPoint.sensor - lowerPoint.sensor)).toFixed(0);

            return interpolatedLitres;
        }

        // Debounce function to limit how often a function can be called
        const debounce = (func, delay) => {
            let debounceTimer;
            return function () {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            };
        };

        function adjustFuelChartPosition() {
            const chart = document.getElementById('fuelChartContainer');
            const rect = chart.getBoundingClientRect();
            const viewportHeight = window.innerHeight;

            if (rect.bottom > viewportHeight) {
                chart.style.top = `${viewportHeight - rect.height}px`;
            }
        }

        // Function to check if the mouse is outside the container
        function isMouseOutsideContainer(container, event) {
            const rect = container.getBoundingClientRect();
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            return mouseX < rect.left || mouseX > rect.right || mouseY < rect.top || mouseY > rect.bottom;
        }

        // Fuel Chart
        let chartInstance = null;

        const sampleFuelData = (data, sampleRate) => {
            return data.filter(data => data.fuelLevel !== '').filter((_, index) => index % sampleRate === 0);
        };

        window.showFuelChart = debounce(async function (event, imei) {
            const fuelChartContainer = document.getElementById('fuelChartContainer');
            const fuelChartCanvas = document.getElementById('fuelChart');

            // Position the chart container near the mouse pointer
            fuelChartContainer.style.left = `${event.pageX - 20}px`;
            fuelChartContainer.style.top = `${event.pageY - 20}px`;
            fuelChartContainer.style.display = 'block';

            // Adjust the position to ensure it doesn't go below the bottom of the screen
            adjustFuelChartPosition();

            // Destroy the existing chart instance if it exists
            if (chartInstance) {
                chartInstance.destroy();
            }

            // Fetch fuel data from the API
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 3);
            const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0).toISOString();
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString();
            const url = `http://dev.inavcloud.com:${selectedPorts}/api/messages_fuel?imei=${imei}&startDate=${startOfDay}&endDate=${endOfDay}`;
            const response = await fetch(url);
            const fuelData = await response.json();

            // Prepare data for the chart
            const sampledChartData = sampleFuelData(fuelData, 10); // Adjust sample rate as needed
            const chartData = sampledChartData.map(data => {
                const timestamp = new Date(data.gpsTimestamp.replace(" ", "T") + "Z");
                const localTime = timestamp.toLocaleString('en-US', options); // Adjust options as needed
                return {
                    x: new Date(localTime),
                    y: {
                        fuelLevel: Number(data.fuelLevel),
                        speed: Number(data.speed),
                        accCode: Number(data.accCode),
                    }
                };
            });

            // Create the bar chart data for ignition periods
            const ignitionData = chartData.map(data => ({
                x: data.x,
                y: data.y.accCode === 1 ? 1 : 0 // Use 1 for ignition on, 0 for off
            }));

            // Create the chart
            chartInstance = new Chart(fuelChartCanvas, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Fuel Level',
                        data: chartData.map(data => ({ x: data.x, y: data.y.fuelLevel })),
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1.5,
                        fill: false,
                        pointRadius: 1.5,
                        yAxisID: 'y-fuel',
                        spanGaps: true
                    }, {
                        label: 'Speed',
                        data: chartData.map(data => ({ x: data.x, y: data.y.speed })),
                        borderColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 1,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0,
                        yAxisID: 'y-speed',
                        spanGaps: true
                    }, {
                        label: 'Ignition',
                        data: ignitionData,
                        type: 'bar',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)', // Light yellow background
                        borderColor: 'rgba(255, 206, 86, 0.1)',
                        borderWidth: 1,
                        yAxisID: 'y-ignition',
                        barPercentage: 1.0,
                        categoryPercentage: 1.0
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'hour',
                                displayFormats: {
                                    hour: 'HH:mm'
                                },
                                min: chartData[0].x,
                                max: chartData[chartData.length - 1].x
                            },
                        },
                        'y-fuel': {
                            type: 'linear',
                            position: 'left',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Fuel Level'
                            }
                        },
                        'y-speed': {
                            type: 'linear',
                            position: 'right',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Speed'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        },
                        'y-ignition': {
                            type: 'linear',
                            position: 'right',
                            display: false,
                            beginAtZero: true,
                            min: 0,
                            max: 1,
                            title: {
                                display: true,
                                text: 'Ignition'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                generateLabels: function (chart) {
                                    const datasets = chart.data.datasets;
                                    return datasets.map((dataset, i) => ({
                                        text: dataset.label,
                                        fillStyle: dataset.borderColor,
                                        hidden: !chart.isDatasetVisible(i),
                                        lineCap: dataset.borderCapStyle,
                                        lineDash: dataset.borderDash,
                                        lineDashOffset: dataset.borderDashOffset,
                                        lineJoin: dataset.borderJoinStyle,
                                        lineWidth: dataset.borderWidth,
                                        strokeStyle: dataset.borderColor,
                                        pointStyle: dataset.pointStyle,
                                        datasetIndex: i
                                    }));
                                }
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            position: 'nearest',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            displayColors: false,
                            callbacks: {
                                label: function (tooltipItem) {
                                    const datasetLabel = tooltipItem.dataset.label || '';
                                    const value = tooltipItem.raw.y;
                                    const accCode = tooltipItem.raw.accCode;
                                    return `${datasetLabel}: ${value}`;
                                },
                            }
                        },
                        zoom: {
                            pan: {
                                enabled: true,
                                mode: 'x',
                                threshold: 10
                            },
                            zoom: {
                                wheel: {
                                    enabled: true
                                },
                                pinch: {
                                    enabled: true
                                },
                                mode: 'x'
                            },
                            limits: {
                                x: {
                                    min: chartData[0].x,
                                    max: chartData[chartData.length - 1].x
                                }
                            }
                        }
                    }
                }
            });
        }, 300);

        // Hide the chart when the mouse leaves the temperature chart container
        document.getElementById('fuelChartContainer').addEventListener('mouseout', function (event) {
            if (!event.relatedTarget || !event.currentTarget.contains(event.relatedTarget)) {
                document.getElementById('fuelChartContainer').style.display = 'none';
            }
        });

        // Check if the mouse is already outside when the chart is created
        const fuelChartContainer = document.getElementById('fuelChartContainer');
        document.addEventListener('mousemove', function (event) {
            if (isMouseOutsideContainer(fuelChartContainer, event)) {
                fuelChartContainer.style.display = 'none';
            }
        });


        // Hide the chart when the mouse leaves the fuel chart container
        document.getElementById('fuelChartContainer').addEventListener('mouseout', function (event) {
            // Check if the mouse has left the container
            if (!event.relatedTarget || !event.currentTarget.contains(event.relatedTarget)) {
                document.getElementById('fuelChartContainer').style.display = 'none';
            }
        });

        function adjustTemperatureChartPosition() {
            const chart = document.getElementById('temperatureChartContainer');
            const rect = chart.getBoundingClientRect();
            const viewportHeight = window.innerHeight;

            if (rect.bottom > viewportHeight) {
                chart.style.top = `${viewportHeight - rect.height}px`;
            }
        }

        // Temperature Chart
        const sampleTemperatureData = (data, sampleRate) => {
            return data.filter(data => data.temperature !== '').filter((_, index) => index % sampleRate === 0);
        };

        window.showTemperatureChart = debounce(async function (event, imei) {
            const temperatureChartContainer = document.getElementById('temperatureChartContainer');
            const temperatureChartCanvas = document.getElementById('temperatureChart');

            // Position the chart container near the mouse pointer
            temperatureChartContainer.style.left = `${event.pageX - 20}px`;
            temperatureChartContainer.style.top = `${event.pageY - 20}px`;
            temperatureChartContainer.style.display = 'block';

            adjustTemperatureChartPosition();

            if (chartInstance) {
                chartInstance.destroy();
            }

            // Fetch temperature data from the API
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 3);
            const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0).toISOString();
            const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString();
            const url = `http://dev.inavcloud.com:${selectedPorts}/api/messages_temperature?imei=${imei}&startDate=${startOfDay}&endDate=${endOfDay}`;
            const response = await fetch(url);
            //console.log(url);
            const temperatureData = await response.json();

            // Prepare data for the chart
            const sampledChartData = sampleTemperatureData(temperatureData, 10); // Adjust sample rate as needed
            const chartData = sampledChartData.map(data => {
                const timestamp = new Date(data.gpsTimestamp.replace(" ", "T") + "Z");
                const localTime = timestamp.toLocaleString('en-US', options); // Adjust options as needed

                // Extract only the first temperature value if multiple values are present (split by '|')
                let tempValue = data.temperature;
                if (tempValue && tempValue.includes('|')) {
                    tempValue = tempValue.split(' | ')[0]; // Get only the first temperature value
                }

                return {
                    x: new Date(localTime),
                    y: {
                        temperature: Number(tempValue),
                        speed: Number(data.speed) // Assuming 'speed' is the key for speed data
                    }
                }
            });

            // Create the chart
            chartInstance = new Chart(temperatureChartCanvas, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Temperature',
                        data: chartData.map(data => ({ x: data.x, y: data.y.temperature })),
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1.5,
                        fill: false,
                        pointRadius: 1.5,
                        yAxisID: 'y-temp',
                        spanGaps: true
                    }, {
                        label: 'Speed',
                        data: chartData.map(data => ({ x: data.x, y: data.y.speed })),
                        borderColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 1,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0,
                        yAxisID: 'y-speed',
                        spanGaps: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'hour',
                                displayFormats: {
                                    hour: 'HH:mm'
                                },
                                min: chartData[0].x,
                                max: chartData[chartData.length - 1].x
                            },
                        },
                        'y-temp': {
                            type: 'linear',
                            position: 'left',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Temperature'
                            }
                        },
                        'y-speed': {
                            type: 'linear',
                            position: 'right',
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: 'Speed'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                generateLabels: function (chart) {
                                    const datasets = chart.data.datasets;
                                    return datasets.map((dataset, i) => ({
                                        text: dataset.label,
                                        fillStyle: dataset.borderColor,
                                        hidden: !chart.isDatasetVisible(i),
                                        lineCap: dataset.borderCapStyle,
                                        lineDash: dataset.borderDash,
                                        lineDashOffset: dataset.borderDashOffset,
                                        lineJoin: dataset.borderJoinStyle,
                                        lineWidth: dataset.borderWidth,
                                        strokeStyle: dataset.borderColor,
                                        pointStyle: dataset.pointStyle,
                                        datasetIndex: i
                                    }));
                                }
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            position: 'nearest',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            displayColors: false,
                            callbacks: {
                                label: function (tooltipItem) {
                                    const datasetLabel = tooltipItem.dataset.label || '';
                                    const value = tooltipItem.raw.y;
                                    return `${datasetLabel}: ${value}`;
                                }
                            }
                        },
                        zoom: {
                            pan: {
                                enabled: true,
                                mode: 'x',
                                threshold: 10
                            },
                            zoom: {
                                wheel: {
                                    enabled: true
                                },
                                pinch: {
                                    enabled: true
                                },
                                mode: 'x'
                            },
                            limits: {
                                x: {
                                    min: chartData[0].x,
                                    max: chartData[chartData.length - 1].x
                                }
                            }
                        }
                    }
                }
            });
        }, 300);

        // Hide the chart when the mouse leaves the temperature chart container
        document.getElementById('temperatureChartContainer').addEventListener('mouseout', function (event) {
            if (!event.relatedTarget || !event.currentTarget.contains(event.relatedTarget)) {
                document.getElementById('temperatureChartContainer').style.display = 'none';
            }
        });

        // Check if the mouse is already outside when the chart is created
        const temperatureChartContainer = document.getElementById('temperatureChartContainer');
        document.addEventListener('mousemove', function (event) {
            if (isMouseOutsideContainer(temperatureChartContainer, event)) {
                temperatureChartContainer.style.display = 'none';
            }
        });

        // Initialize the map
        document.addEventListener('DOMContentLoaded', () => {

            //checkAuth();

            const map = L.map('map').setView([12.3, 122], 6);

            // Use standard OpenStreetMap tiles (CORS-friendly)
            const osm = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19,
                crossOrigin: true
            });

            // Use Esri satellite imagery (CORS-friendly alternative to Google)
            const esriSat = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19,
                crossOrigin: true
            });

            // Use OpenStreetMap with traffic overlay (alternative to Google Traffic)
            const osmTraffic = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                opacity: 0.7,
                maxZoom: 19,
                crossOrigin: true
            });

            // Add fallback tile layer in case primary sources fail
            const fallbackTiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors (Fallback)',
                maxZoom: 19,
                crossOrigin: true
            });

            // Add error handling for tile loading
            osm.on('tileerror', function(error, tile) {
                console.warn('OSM tile failed to load:', error);
            });

            esriSat.on('tileerror', function(error, tile) {
                console.warn('Esri satellite tile failed to load:', error);
            });

            const baseMaps = {
                "OpenStreetMap": osm,
                "Satellite": esriSat,
                "Fallback": fallbackTiles
            };

            const overlayMaps = {
                "Traffic": osmTraffic
            };

            L.control.layers(baseMaps, overlayMaps).addTo(map);

            // Set default layer
            osm.addTo(map);

            // Add street view by right clicking on the map
            map.on('contextmenu', function (event) {
                let lat = event.latlng.lat;
                let lng = event.latlng.lng;
                window.open(`https://www.google.com/maps?q=&layer=c&cbll=${lat},${lng}&cbp=11,0,0,0,0`);
            });

            let zoomAdjusted = false;
            let isUpdating = false;
            const markers = {};
            let allLatLngs = [];
            let isListExpanded = false; // Track whether the list is expanded or collapsed

            // Detect map adjust
            map.on('moveend', (e) => {
                zoomAdjusted = true;
            })

            document.getElementById('toggleButton').addEventListener('click', () => {
                setTimeout(() => {
                    map.invalidateSize();
                }, 200);
            });

            const savedCsvData = localStorage.getItem('csvData');
            if (savedCsvData) {
                document.getElementById('csvInput').value = savedCsvData;
                const csvLines = savedCsvData.split('\n');
                const imeiData = csvLines.map(line => {
                    let [imei, name] = line.split(',');
                    name = name ? name.trim() : '';
                    return { imei, name };
                });
                plotLocations(imeiData);
            }

            document.getElementById('csvForm').addEventListener('submit', function (event) {
                event.preventDefault();

                const csvInput = document.getElementById('csvInput').value.trim();
                localStorage.setItem('csvData', csvInput);

                const csvLines = csvInput.split('\n');
                const imeiData = csvLines.map(line => {
                    let [imei, name] = line.split(',');
                    name = name ? name.trim() : '';
                    return { imei: imei.trim(), name: name.trim() };
                });

                location.reload();
            });

            document.getElementById('deleteDataButton').addEventListener('click', handleDeleteDataClick);

            let deleteClickCount = 0;
            let clickTimer;

            function handleDeleteDataClick() {
                deleteClickCount++;

                clearTimeout(clickTimer);
                clickTimer = setTimeout(async () => {
                    if (deleteClickCount === 3) {
                        plotAllLocations(); // Re-plot all locations after deletion
                    } else {
                        deleteData(); // Just delete the data
                    }
                    deleteClickCount = 0;
                }, 500);
            }

            function deleteData() {
                localStorage.removeItem('csvData');
                localStorage.removeItem('accumulatedUserData');
                document.getElementById('csvInput').value = '';
                location.reload();
            }

            async function fetchLocation(imei) {

                // Check if selectedPorts is populated, if not, wait until it is populated
                if (selectedPorts.length === 0) {
                    await new Promise(resolve => {
                        const checkPortsInterval = setInterval(() => {
                            if (selectedPorts.length > 0) {
                                clearInterval(checkPortsInterval);
                                resolve();
                            }
                        }, 100); // Check every 100ms
                    });
                }

                try {
                    // Use Promise.any to get the first successful fetch
                    const data = await Promise.any(selectedPorts.map(async (port) => {
                        const url = `http://dev.inavcloud.com:${port}/api/messages/last?imei=${imei}`;
                        console.log(`Fetching url: ${url}`);
                        const response = await fetch(url);

                        const result = await response.json();
                        return Object.assign(result, { port: `${port}` });
                    }))
                    //populateCsvForm([data]);
                    return data;
                } catch (error) {
                    console.error(`Error fetching data:`, error);
                    return null;
                }
            }

            async function fetchAllLocation() {

                let data;
                let error;
                let attempts = 0;
                const maxRetries = 3;
                while (attempts < maxRetries) {
                    try {
                        const fetchPromises = selectedPorts.map(async (port) => {
                            const url = `http://dev.inavcloud.com:${port}/api/messages/last/all`;
                            console.log(`Fetching url: ${url}`);
                            const response = await fetch(url);
                            const data = await response.json();
                            return data.map(record => ({ ...record, port })); // Add port to each record
                        });
                        data = await Promise.all(fetchPromises);
                        //populateCsvForm(data.flat());
                        return data.flat(); // Flatten the array and return it
                    } catch (err) {
                        error = err;
                        attempts += 1;
                    }
                }
                console.error(`Error fetching data after ${maxRetries} attempts:`, error);
                return null;
            }

            function populateCsvForm(data) {
                const csvLines = data.map(record => {
                    const imei = record.imei;
                    const name = accumulatedUserData[imei].name || '';
                    return `${imei},${name} `;
                }).join('\n');

                document.getElementById('csvInput').value = csvLines;
                localStorage.setItem('csvData', csvLines);
            }

            // Get the modal
            const modal = document.getElementById("commandModal");

            // Get the <span> element that closes the modal
            const span = document.getElementsByClassName("close")[0];

            // Define global variables for EventSource management
            let activeEventSource = {};
            let messageTimeout;
            let reconnectTimeout;
            let lastErrorTime = {};
            let reconnectAttempts = {};

            // Constants for EventSource management
            const MESSAGE_TIMEOUT = 60 * 1000;  // 60 seconds timeout for no messages
            const MAX_BACKOFF = 30 * 1000; // Maximum backoff time (30 seconds)

            // When the user clicks on <span> (x), close the modal
            span.onclick = function () {
                modal.style.display = "none";

                // Clean up any active EventSource connections
                for (const imei in activeEventSource) {
                    if (activeEventSource[imei]) {
                        activeEventSource[imei].close();
                        activeEventSource[imei] = null;
                    }
                }

                // Clear all timeouts
                clearTimeout(messageTimeout);
                clearTimeout(reconnectTimeout);

                // Reset the raw data response
                if (rawDataResponse) {
                    rawDataResponse.innerHTML = '';
                }
            }

            // When the user clicks anywhere outside of the modal, close it
            window.onclick = function (event) {
                if (event.target == modal) {
                    modal.style.display = "none";

                    // Clean up any active EventSource connections
                    for (const imei in activeEventSource) {
                        if (activeEventSource[imei]) {
                            activeEventSource[imei].close();
                            activeEventSource[imei] = null;
                        }
                    }

                    // Clear all timeouts
                    clearTimeout(messageTimeout);
                    clearTimeout(reconnectTimeout);

                    // Reset the raw data response
                    if (rawDataResponse) {
                        rawDataResponse.innerHTML = '';
                    }
                }
            }

            // Handle form submission
            const commandForm = document.getElementById('commandForm');
            const commandResponse = document.getElementById('commandResponse');
            const rawDataResponse = document.getElementById('rawDataResponse');
            const timeoutDuration = 120 * 1000;
            const maxRetries = 1; // Set the maximum number of retry attempts

            commandForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const imei = document.getElementById('modalImei').value;
                const command = document.getElementById('modalCommand').value;

                commandResponse.textContent = 'Sending command...';

                let attempts = 0;
                let success = false;

                while (attempts < maxRetries && !success) {
                    attempts += 1;
                    try {
                        // Set a timeout to handle the command sending
                        const timeout = new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Command sending timed out')), timeoutDuration)
                        );

                        // Use Promise.race to handle both the fetch request and timeout
                        const response = await Promise.race([
                            fetch(`http://dev.inavcloud.com:${selectedPorts}/api/send-command?imei=${imei}&command=${command}`),
                            timeout
                        ]);

                        if (!response.ok) {
                            throw new Error('Timeout. Resending in background.');
                        }

                        // Formatting response in JSON
                        const data = await response.json();
                        // Display command response from the device
                        commandResponse.textContent = `Reply: ${(JSON.stringify(data)).replace(/[{}]/g, '')}`;
                        commandResponse.className = 'responseCommand success';
                        success = true; // Mark as successful if no error occurred
                    } catch (error) {
                        if (attempts >= maxRetries) {
                            commandResponse.textContent = `Error: ${error.message}`;
                            commandResponse.className = 'responseCommand error';
                            break;
                        }

                        // Add a delay before retrying (e.g., 3 seconds)
                        await new Promise(resolve => setTimeout(resolve, 5 * 1000));
                    }
                }
            });

            // Marker list handler
            const markerList = document.getElementById('markerList');
            markerList.addEventListener('click', (e) => {
                if (!e.target.classList.contains('material-icons-outlined')) {
                    const item = e.target.closest('.marker-list-item');
                    if (item) {
                        const imei = item.getAttribute('data-imei');
                        Object.values(markers).forEach(marker => marker.closePopup());
                        const messageDetails = item.querySelector('.message-details');
                        messageDetails.style.display = messageDetails.style.display === 'block' ? 'none' : 'block';
                        rawDataResponse.innerHTML = ''; // Clear previous response

                        // Toggle the expansion state of the list
                        isListExpanded = messageDetails.style.display === 'block';

                        if (isListExpanded) {
                            const latLng = markers[imei].getLatLng();
                            map.setView(latLng, 15); // Zoom to the marker
                            markers[imei].openPopup(); // Open the popup
                        }
                    }
                }
            });

            // Event delegation for open-button
            markerList.addEventListener('click', (e) => {
                if (e.target.classList.contains('material-icons-outlined')) {
                    e.stopPropagation(); // Prevent marker list from collapsing
                    const item = e.target.closest('.marker-list-item');
                    const imei = item.getAttribute('data-imei');
                    document.getElementById('modalImei').value = imei; // Set the IMEI in the modal
                    commandResponse.innerHTML = ''; // Clear previous response
                    commandResponse.className = 'response';
                    modal.style.display = "block"; // Show the modal

                    function createEventSource(imei) {
                        // If EventSource already exists, close it before creating a new one
                        if (activeEventSource[imei]) {
                            activeEventSource[imei].close();
                        }

                        // Initialize EventSource
                        activeEventSource[imei] = new EventSource(`http://dev.inavcloud.com:${selectedPorts}/api/device/raw?imei=${imei}`);

                        // Set up a timeout to track if no message is received
                        clearTimeout(messageTimeout);
                        messageTimeout = setTimeout(() => {
                            if (activeEventSource[imei]) {
                                activeEventSource[imei].close();
                                retryConnection(imei, "No messages received");
                            }
                        }, MESSAGE_TIMEOUT);

                        // EventSource message handler
                        activeEventSource[imei].onmessage = (event) => {
                            // Reset reconnect attempts on successful message
                            reconnectAttempts[imei] = 0;

                            clearTimeout(messageTimeout);  // Reset the timeout whenever a message is received

                            const serverTimestamp = new Date().toLocaleString('en-US', options).replace(',', '');

                            // Create a new div for each message
                            const dataElement = document.createElement('div');
                            dataElement.textContent = `[${serverTimestamp}]: ${event.data}`;

                            // Append the new message to the rawDataResponse container
                            rawDataResponse.appendChild(dataElement);

                            // Add an empty line between messages
                            const emptyLine = document.createElement('div');
                            emptyLine.innerHTML = ' ';
                            rawDataResponse.appendChild(emptyLine);

                            // Auto scroll to bottom
                            rawDataResponse.scrollTop = rawDataResponse.scrollHeight;

                            // Reset the timeout again after handling the message
                            clearTimeout(messageTimeout);
                            messageTimeout = setTimeout(() => {
                                if (activeEventSource[imei]) {
                                    activeEventSource[imei].close();
                                    retryConnection(imei);
                                }
                            }, MESSAGE_TIMEOUT);
                        };

                        // EventSource error handler
                        activeEventSource[imei].onerror = (event) => {
                            console.error(`EventSource error: ${imei}`, event);

                            // Update the last error time
                            lastErrorTime[imei] = Date.now();

                            // Close the connection
                            if (activeEventSource[imei]) {
                                activeEventSource[imei].close();
                            }

                            // Implement exponential backoff for reconnection
                            if (!reconnectAttempts[imei]) {
                                reconnectAttempts[imei] = 1;
                            } else {
                                reconnectAttempts[imei]++;
                            }

                            const backoffTime = 30 * 1000;

                            // Show error message to user
                            const dataElement = document.createElement('div');
                            dataElement.textContent = `Waiting for device to connect in ${backoffTime/1000} seconds...`;
                            dataElement.style.color = 'red';
                            rawDataResponse.appendChild(dataElement);

                            // Auto scroll to bottom
                            rawDataResponse.scrollTop = rawDataResponse.scrollHeight;

                            // Schedule reconnection with backoff
                            clearTimeout(reconnectTimeout);
                            reconnectTimeout = setTimeout(() => {
                                retryConnection(imei);
                            }, backoffTime);
                        };
                    }

                    // Retry connection function
                    function retryConnection(imei, reason) {
                        createEventSource(imei);
                    }

                    // Initialize the first connection
                    createEventSource(imei);

                    // Add an event listener for when the modal is closed
                    const closeModalButton = document.querySelector('.close');
                    closeModalButton.addEventListener('click', () => {
                        rawDataResponse.innerHTML = ''; // Clear the raw data response
                        if (activeEventSource[imei]) {
                            activeEventSource[imei].close(); // Close the EventSource connection
                            activeEventSource[imei] = null; // Remove the reference
                        }
                        // Clear all timeouts
                        clearTimeout(messageTimeout);
                        clearTimeout(reconnectTimeout);

                        // Reset reconnection state for this device
                        reconnectAttempts[imei] = 0;
                        lastErrorTime[imei] = 0;
                    });
                }
            });


            async function plotLocations(imeiData) {
                if (isUpdating) return;
                isUpdating = true;
                const spinner = document.getElementById('spinner');
                spinner.style.display = 'block';

                const fragment = document.createDocumentFragment(); // Create a fragment for batch updates

                // Initialize counters
                let allCount = 0;
                let onlineCount = 0;
                let offlineCount = 0;
                let delayCount = 0;
                let runningCount = 0;
                let idlingCount = 0;
                let ignitionOffCount = 0;
                let status;

                await loadAccumulatedUserData(selectedPorts);

                for (const { imei, name } of imeiData) {
                    const data = await fetchLocation(imei);

                    let newName = name || '';
                    let brand = '';
                    let endSub = '';
                    let sim = '';
                    let isExpired = false;
                    let calibration = '';
                    // Remove fuelTable initialization here

                    if (accumulatedUserData[imei] && !name) {
                        newName = name ? name : accumulatedUserData[imei].name || ' ';
                        brand = accumulatedUserData[imei].brand || '';
                        endSub = new Date(accumulatedUserData[imei].endSub).toLocaleDateString('en-CA') || '';
                        sim = accumulatedUserData[imei].sim || '';
                        isExpired = new Date(accumulatedUserData[imei].endSub) < new Date();
                        calibration = accumulatedUserData[imei].calibration || '';
                    }

                    // Create fuelTable after we have the calibration data
                    const fuelTable = calibration ? csvToFuelTable(calibration) : [];

                    if (data && !data.error) {
                        const { serverTimestamp, gpsTimestamp, latitude, longitude, speed, course, satelliteCount, sosCode, accCode, doorCode, temperature, mccMnc, gsm, extBat, intBat, fuelLevel, analogIn, port } = data;

                        allCount++;
                        let isConnected;
                        (new Date() - new Date(serverTimestamp)) < 2 * 60 * 1000 ? isConnected = true : isConnected = false;

                        if (gpsTimestamp) {
                            const latLng = [parseFloat(latitude), parseFloat(longitude)];
                            allLatLngs.push(latLng);

                            // Formatting and data processing
                            let timestamp = new Date(gpsTimestamp.replace(" ", "T") + "Z");
                            const formattedDate = timestamp.toLocaleString('en-CA', options).replace(/\//g, '-').replace(',', '').replace(/[.]/g, '');

                            // Calculate the delay in milliseconds (current UTC time - GPS timestamp)
                            const delay = new Date().getTime() - timestamp.getTime();
                            //console.log(timestamp, delay); // Delay in hours

                            // Detemine icon and status
                            let iconUrl;
                            if (delay > 48 * 60 * 60 * 1000) {
                                iconUrl = icon_offline;
                                status = 'Delay > 2 days';
                                offlineCount++;
                            } else if (delay > 1 * 60 * 60 * 1000) {
                                iconUrl = icon_delay;
                                status = 'Delay'
                                delayCount++;
                            } else if (parseInt(speed) > 0) {
                                iconUrl = parseFloat(temperature.split(' | ')[0]) < 0 ? icon_freezer : icon_running;
                                status = 'Running';
                                runningCount++;
                            } else {
                                iconUrl = parseInt(accCode) === 1 ? icon_stopped : icon_ignition_off;
                                status = parseInt(accCode) === 1 ? 'Idling' : 'Ignition Off';
                                if (status === 'Idling') {
                                    idlingCount++;
                                } else {
                                    ignitionOffCount++;
                                }
                            }
                            onlineCount = delayCount + runningCount + idlingCount + ignitionOffCount;

                            const iconHtml = `
                                <div class="car-icon">
                                <div class="rotate-icon" style="transform: rotate(${course - 90}deg);">
                                    <img src="${iconUrl}" />
                                </div>
                                </div>
                            `;
                            /*
                            const iconHtml = `
                                <div class="car-icon">
                                <div class="rotate-icon" style="transform: rotate(${course - 90}deg);">
                                    <img src="${iconUrl}" />
                                </div>
                                <div class="car-icon-text">${newName}</div>
                                </div>
                            `;
                            */

                            const popupContent = `
                                <b>${newName}</b><br>
                                IMEI: ${imei}<br>
                                Time: ${formattedDate}<br>
                                Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                ${temperature ? ` - Temp: ${temperature}` : ''}
                                ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                            `;

                            if (markers[imei]) {
                                // Update car icon name
                                //const nameText = markers[imei]._icon.querySelector('.car-icon-text');
                                //nameText.textContent = newName;

                                const currentLatLng = markers[imei].getLatLng();
                                if (currentLatLng.lat !== latLng[0] || currentLatLng.lng !== latLng[1]) {
                                    markers[imei].setLatLng(latLng);
                                    markers[imei].setPopupContent(popupContent);
                                    // Update icon
                                    const iconImg = markers[imei]._icon.querySelector('img');
                                    iconImg.src = iconUrl;

                                    const rotateIcon = markers[imei]._icon.querySelector('.rotate-icon');
                                    rotateIcon.style.transform = `rotate(0deg)`;
                                    setTimeout(() => {
                                        rotateIcon.style.transform = `rotate(${course - 90}deg)`;
                                    }, 0);
                                }
                            } else {
                                markers[imei] = L.marker(latLng, {
                                    icon: L.divIcon({
                                        className: '',
                                        html: iconHtml,
                                        iconSize: [60, 60],
                                        iconAnchor: [30, 30],
                                        popupAnchor: [0, -15]
                                    })
                                }).bindPopup(popupContent).addTo(map);

                                markers[imei].on('click', function (e) {
                                    markers[imei].openPopup();

                                    var context = document.querySelector("#markerList");
                                    if (context) {
                                        var instance = new Mark(context);
                                        instance.unmark();
                                        instance.mark(`${imei}`, {
                                            done: function () {
                                                // Find the first marked element and scroll it into view
                                                var markedElement = context.querySelector("mark");
                                                if (markedElement) {
                                                    markedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                                }
                                            }
                                        });
                                    }
                                });
                            }

                            // Update marker list UI in the fragment
                            const existingItem = markerList.querySelector(`[data-imei="${imei}"]`);
                            if (!existingItem) {
                                const listItem = document.createElement('div');
                                listItem.className = 'marker-list-item';
                                listItem.setAttribute('data-imei', imei);
                                listItem.innerHTML = `
                                    <div class="marker-title">
                                    <b>${newName}</b><br>
                                    Imei: ${imei}<br>
                                    ${sim ? `Sim: ${sim}<br>` : ''}
                                    Time: ${formattedDate}<br>
                                    ${endSub ? `End: ${endSub}${isExpired ? '<span style="color: red;"> Expired</span>' : ''}<br>` : ''}
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">${status} - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                    </div>
                                    <div class="message-details" style="display: none;">
                                    Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                    ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                    ${temperature ? ` - Temp: ${temperature}` : ''}
                                    ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                    ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                    ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                    ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                    ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                                    <br>
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    <button class="replay-button"><i class="material-icons-outlined">play_arrow</i></button>
                                    <button class="reports-button"><i class="material-icons-outlined">description</i></button>
                                    ${fuelLevel ? `<button class="fuel-button"><i class="material-icons-outlined">local_gas_station</i></button>` : ''}
                                    ${temperature ? `<button class="temperature-button"><i class="material-icons-outlined">device_thermostat</i></button>` : ''}
                                    </div>
                                `;

                                // Add click listener for replay button
                                listItem.querySelector('.replay-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const replayImei = imei;
                                    const replayPort = port;
                                    const replayName = newName;
                                    const reportsCalibration = calibration;
                                    window.open(`/replay?imei=${replayImei}&port=${replayPort}&name=${replayName}&calibration=${reportsCalibration}`, '_blank');
                                });

                                // Add click listener for reports button
                                listItem.querySelector('.reports-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const reportsImei = imei;
                                    const reportsPort = port;
                                    const reportsName = newName;
                                    const reportsCalibration = calibration;
                                    window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                });

                                // Add mouseover listener for fuel button
                                const fuelButton = listItem.querySelector('.fuel-button');
                                if (fuelButton) {
                                    fuelButton.removeEventListener('mouseover', showFuelChart);
                                    fuelButton.addEventListener('mouseover', function (event) {
                                        showFuelChart(event, imei);
                                    });

                                    // Add click listener for fuel button
                                    listItem.querySelector('.fuel-button').addEventListener('click', function () {
                                        event.stopPropagation(); // Prevent marker list from collapsing
                                        const reportsImei = imei;
                                        const reportsPort = port;
                                        const reportsName = newName;
                                        window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}`, '_blank');
                                    });
                                }

                                // Add mouseover listener for temperature button
                                const temperatureButton = listItem.querySelector('.temperature-button');
                                if (temperatureButton) {
                                    temperatureButton.removeEventListener('mouseover', showFuelChart);
                                    temperatureButton.addEventListener('mouseover', function (event) {
                                        showTemperatureChart(event, imei);
                                    });
                                }

                                fragment.appendChild(listItem); // Append to fragment instead of directly to DOM

                            } else {
                                existingItem.querySelector('.marker-title').innerHTML = `
                                    <b>${newName}</b><br>
                                    Imei: ${imei}<br>
                                    ${sim ? `Sim: ${sim}<br>` : ''}
                                    Time: ${formattedDate}<br>
                                    ${endSub ? `End: ${endSub}${isExpired ? '<span style="color: red;"> Expired</span>' : ''}<br>` : ''}
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">${status} - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                `;
                                existingItem.querySelector('.message-details').innerHTML = `
                                    Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                    ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                    ${temperature ? ` - Temp: ${temperature}` : ''}
                                    ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                    ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                    ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                    ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                    ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                                    <br>
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    <button class="replay-button"><i class="material-icons-outlined">play_arrow</i></button>
                                    <button class="reports-button"><i class="material-icons-outlined">description</i></button>
                                    ${fuelLevel ? `<button class="fuel-button"><i class="material-icons-outlined">local_gas_station</i></button>` : ''}
                                    ${temperature ? `<button class="temperature-button"><i class="material-icons-outlined">device_thermostat</i></button>` : ''}
                                `;

                                // Add click listener for replay button
                                existingItem.querySelector('.replay-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const replayImei = imei;
                                    const replayPort = port;
                                    const replayName = newName;
                                    const replayCalibration = calibration;
                                    window.open(`/replay?imei=${replayImei}&port=${replayPort}&name=${replayName}&calibration=${replayCalibration}`, '_blank'); // open in a new tab
                                });

                                // Add click listener for reports button
                                existingItem.querySelector('.reports-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const reportsImei = imei;
                                    const reportsPort = port;
                                    const reportsName = newName;
                                    const reportsCalibration = calibration;
                                    window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                });

                                // Add mouseover listener for fuel button
                                const fuelButton = existingItem.querySelector('.fuel-button');
                                if (fuelButton) {
                                    fuelButton.removeEventListener('mouseover', showFuelChart);
                                    fuelButton.addEventListener('mouseover', function (event) {
                                        showFuelChart(event, imei);
                                    });

                                    // Add click listener for fuel button
                                    existingItem.querySelector('.fuel-button').addEventListener('click', function () {
                                        event.stopPropagation(); // Prevent marker list from collapsing
                                        const reportsImei = imei;
                                        const reportsPort = port;
                                        const reportsName = newName;
                                        const reportsCalibration = calibration;
                                        window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                    });
                                }

                                // Add mouseover listener for temperature button
                                const temperatureButton = existingItem.querySelector('.temperature-button');
                                if (temperatureButton) {
                                    temperatureButton.removeEventListener('mouseover', showFuelChart);
                                    temperatureButton.addEventListener('mouseover', function (event) {
                                        showTemperatureChart(event, imei);
                                    });
                                }
                            }
                        } else {
                            offlineCount++;
                            // Update marker list UI of online markers without gpsTimestamp in the fragment
                            const existingItem = markerList.querySelector(`[data-imei="${imei}"]`);
                            if (!existingItem) {
                                const listItem = document.createElement('div');
                                listItem.className = 'marker-list-item';
                                listItem.setAttribute('data-imei', imei);
                                listItem.innerHTML = `
                                    <div class="marker-title">
                                    <b>${newName}</b><br>Imei: ${imei}<br>
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">no GPS data - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                    </div>
                                    <div class="message-details" style="display: none;">
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    </div>
                                `;
                                fragment.appendChild(listItem); // Append to fragment instead of directly to DOM
                            }
                        }

                    } else {
                        // Update marker list UI of offline markers in the fragment
                        allCount++;
                        offlineCount++;
                        status = 'Never Online';
                        const existingItem = markerList.querySelector(`[data-imei="${imei}"]`);
                        if (!existingItem) {
                            const listItem = document.createElement('div');
                            listItem.className = 'marker-list-item';
                            listItem.setAttribute('data-imei', imei);
                            listItem.innerHTML = `
                                <div class="marker-title">
                                <b>${newName}</b><br>Imei: ${imei}<br>
                                Status: <span style="color: red;">${status}</span>
                                </div>
                                <div class="message-details" style="display: none;">
                                ${brand ? `Brand: ${brand}<br>` : ''}
                                <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                </div>
                                `;
                            fragment.appendChild(listItem); // Append to fragment instead of directly to DOM
                        }
                    }

                    markerList.appendChild(fragment); // Append all updates at once
                }

                // Update count container
                document.getElementById('allCount').textContent = allCount;
                document.getElementById('onlineCount').textContent = onlineCount;
                document.getElementById('offlineCount').textContent = offlineCount;
                document.getElementById('delayCount').textContent = delayCount;
                document.getElementById('runningCount').textContent = runningCount;
                document.getElementById('idlingCount').textContent = idlingCount;

                if (!zoomAdjusted && allLatLngs.length > 0) {
                    const bounds = L.latLngBounds(allLatLngs);
                    map.fitBounds(bounds);
                    zoomAdjusted = true;
                }

                spinner.style.display = 'none'; // Hide spinner after fetching is complete
                isUpdating = false;

                setTimeout(() => plotLocations(imeiData), 10 * 1000); // Schedule after 10 seconds
            }

            async function plotAllLocations() {
                if (isUpdating) return;
                isUpdating = true;

                const spinner = document.getElementById('spinner');
                spinner.style.display = 'block';

                await loadAccumulatedUserData(selectedPorts);

                const fragment = document.createDocumentFragment(); // Create a fragment for batch updates
                const data = await fetchAllLocation();

                // Initialize counters
                let allCount = 0;
                let onlineCount = 0;
                let offlineCount = 0;
                let delayCount = 0;
                let runningCount = 0;
                let idlingCount = 0;
                let ignitionOffCount = 0;

                const batchSize = 50; // Define the batch size (number of records per chunk)
                let batchIndex = 0;

                function processBatch() {
                    const start = batchIndex * batchSize;
                    const end = Math.min(start + batchSize, data.length);
                    const batchData = data.slice(start, end);

                    for (const record of batchData) {
                        const { imei, serverTimestamp, gpsTimestamp, latitude, longitude, speed, course, satelliteCount, sosCode, accCode, doorCode, temperature, mccMnc, gsm, extBat, intBat, fuelLevel, analogIn, port } = record;
                        allCount++;
                        let isConnected;
                        (new Date() - new Date(serverTimestamp)) < 2 * 60 * 1000 ? isConnected = true : isConnected = false;

                        let name = '';
                        let brand = '';
                        let endSub = '';
                        let sim = '';
                        let isExpired = false;
                        let calibration = '';
                        let fuelTable;

                        if (accumulatedUserData[imei]) {
                            name = accumulatedUserData[imei].name || '';
                            brand = accumulatedUserData[imei].brand || '';
                            endSub = accumulatedUserData[imei].endSub ? new Date(accumulatedUserData[imei].endSub).toLocaleDateString('en-CA') : '';
                            sim = accumulatedUserData[imei].sim || '';
                            isExpired = new Date(accumulatedUserData[imei].endSub) < new Date();
                            calibration = accumulatedUserData[imei].calibration || '';
                            fuelTable = calibration ? csvToFuelTable(calibration) : [];
                        }

                        //console.log(name, brand, endSub, sim, isExpired, calibration);

                        if (gpsTimestamp) {
                            const latLng = [parseFloat(latitude), parseFloat(longitude)];
                            allLatLngs.push(latLng);

                            // Formatting and data processing
                            let timestamp = new Date(gpsTimestamp.replace(" ", "T") + "Z");
                            const formattedDate = timestamp.toLocaleString('en-CA', options).replace(/\//g, '-').replace(',', '').replace(/[.]/g, '');

                            // Calculate the delay in milliseconds (current UTC time - GPS timestamp)
                            const delay = new Date().getTime() - timestamp.getTime();
                            //console.log(timestamp, delay); // Delay in hours

                            let iconUrl, status;
                            if (delay > 48 * 60 * 60 * 1000) {
                                iconUrl = icon_offline;
                                status = 'Delay > 2 days';
                                offlineCount++;
                            } else if (delay > 1 * 60 * 60 * 1000) {
                                iconUrl = icon_delay;
                                status = 'Delay';
                                delayCount++;
                            } else if (parseInt(speed) > 0) {
                                iconUrl = parseFloat(temperature) < 0 ? icon_freezer : icon_running;
                                status = 'Running';
                                runningCount++;
                            } else {
                                iconUrl = parseInt(accCode) === 1 ? icon_stopped : icon_ignition_off;
                                status = parseInt(accCode) === 1 ? 'Idling' : 'Ignition Off';
                                if (status === 'Idling') {
                                    idlingCount++;
                                } else {
                                    ignitionOffCount++;
                                }
                            }
                            onlineCount = delayCount + runningCount + idlingCount + ignitionOffCount;

                            const iconHtml = `
                                <div class="car-icon">
                                <div class="rotate-icon" style="transform: rotate(${course - 90}deg);">
                                    <img src="${iconUrl}" />
                                </div>
                                </div>
                            `;
                            /*
                            const iconHtml = `
                                <div class="car-icon">
                                <div class="rotate-icon" style="transform: rotate(${course - 90}deg);">
                                    <img src="${iconUrl}" />
                                </div>
                                <div class="car-icon-text">${name}</div>
                                </div>
                            `;
                            */

                            const popupContent = `
                                <b>${name}</b><br>
                                IMEI: ${imei}<br>
                                Time: ${formattedDate}<br>
                                Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                ${temperature ? ` - Temp: ${temperature}` : ''}
                                ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                            `;

                            if (markers[imei]) {
                                // Update car icon name
                                //const nameText = markers[imei]._icon.querySelector('.car-icon-text');
                                //nameText.textContent = name;

                                const currentLatLng = markers[imei].getLatLng();
                                if (currentLatLng.lat !== latLng[0] || currentLatLng.lng !== latLng[1]) {
                                    markers[imei].setLatLng(latLng);
                                    markers[imei].setPopupContent(popupContent);
                                    // Update icon
                                    const iconImg = markers[imei]._icon.querySelector('img');
                                    iconImg.src = iconUrl;
                                    const rotateIcon = markers[imei]._icon.querySelector('.rotate-icon');
                                    rotateIcon.style.transform = `rotate(0deg)`;
                                    setTimeout(() => {
                                        rotateIcon.style.transform = `rotate(${course - 90}deg)`;
                                    }, 0);
                                }
                            } else {
                                markers[imei] = L.marker(latLng, {
                                    icon: L.divIcon({
                                        className: '',
                                        html: iconHtml,
                                        iconSize: [60, 60],
                                        iconAnchor: [30, 30],
                                        popupAnchor: [0, -15]
                                    })
                                }).bindPopup(popupContent).addTo(map);

                                markers[imei].on('click', function (e) {
                                    markers[imei].openPopup();

                                    var context = document.querySelector("#markerList");
                                    if (context) {
                                        var instance = new Mark(context);
                                        instance.unmark();
                                        instance.mark(`${imei}`, {
                                            done: function () {
                                                // Find the first marked element and scroll it into view
                                                var markedElement = context.querySelector("mark");
                                                if (markedElement) {
                                                    markedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                                }
                                            }
                                        });
                                    }
                                });
                            }

                            // Update marker list UI in the fragment
                            const existingItem = markerList.querySelector(`[data-imei="${imei}"]`);
                            if (!existingItem) {
                                const listItem = document.createElement('div');
                                listItem.className = 'marker-list-item';
                                listItem.setAttribute('data-imei', imei);
                                listItem.innerHTML = `
                                    <div class="marker-title">
                                    <b>${name}</b><br>
                                    Imei: ${imei}<br>
                                    ${sim ? `Sim: ${sim}<br>` : ''}
                                    Time: ${formattedDate}<br>
                                    ${endSub ? `End: ${endSub}${isExpired ? '<span style="color: red;"> Expired</span>' : ''}<br>` : ''}
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">${status} - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                    </div>
                                    <div class="message-details" style="display: none;">
                                    Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                    ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                    ${temperature ? ` - Temp: ${temperature}` : ''}
                                    ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                    ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                    ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                    ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                    ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                                    <br>
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    <button class="replay-button"><i class="material-icons-outlined">play_arrow</i></button>
                                    <button class="reports-button"><i class="material-icons-outlined">description</i></button>
                                    ${fuelLevel ? `<button class="fuel-button"><i class="material-icons-outlined">local_gas_station</i></button>` : ''}
                                    ${temperature ? `<button class="temperature-button"><i class="material-icons-outlined">device_thermostat</i></button>` : ''}
                                    </div>
                                `;

                                // Add click listener for replay button
                                listItem.querySelector('.replay-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const replayImei = imei;
                                    const replayPort = port;
                                    const replayName = name;
                                    const replayCalibration = calibration;
                                    window.open(`/replay?imei=${replayImei}&port=${replayPort}&name=${replayName}&calibration=${replayCalibration}`, '_blank');
                                });

                                // Add click listener for reports button
                                listItem.querySelector('.reports-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const reportsImei = imei;
                                    const reportsPort = port;
                                    const reportsName = name;
                                    const reportsCalibration = calibration;
                                    window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                });

                                // Add mouseover listener for fuel button
                                const fuelButton = listItem.querySelector('.fuel-button');
                                if (fuelButton) {
                                    fuelButton.removeEventListener('mouseover', showFuelChart);
                                    fuelButton.addEventListener('mouseover', function (event) {
                                        showFuelChart(event, imei);
                                    });

                                    // Add click listener for fuel button
                                    listItem.querySelector('.fuel-button').addEventListener('click', function () {
                                        event.stopPropagation(); // Prevent marker list from collapsing
                                        const reportsImei = imei;
                                        const reportsPort = port;
                                        const reportsName = name;
                                        const reportsCalibration = calibration;
                                        window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                    });
                                }

                                // Add mouseover listener for temperature button
                                const temperatureButton = listItem.querySelector('.temperature-button');
                                if (temperatureButton) {
                                    temperatureButton.removeEventListener('mouseover', showFuelChart);
                                    temperatureButton.addEventListener('mouseover', function (event) {
                                        showTemperatureChart(event, imei);
                                    });
                                }

                                fragment.appendChild(listItem); // Append to fragment instead of directly to DOM

                            } else {
                                existingItem.querySelector('.marker-title').innerHTML = `
                                    <b>${name}</b><br>
                                    Imei: ${imei}<br>
                                    ${sim ? `Sim: ${sim}<br>` : ''}
                                    Time: ${formattedDate}<br>
                                    ${endSub ? `End: ${endSub}${isExpired ? '<span style="color: red;"> Expired</span>' : ''}<br>` : ''}
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">${status} - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                `;
                                existingItem.querySelector('.message-details').innerHTML = `
                                    Speed: ${speed} km/h - Course: ${course}° - Sat: ${satelliteCount} - Acc: ${accCode}
                                    ${fuelLevel ? ` - cFuel: ${getFuelLevel(fuelLevel, fuelTable)}` : ''}
                                    ${temperature ? ` - Temp: ${temperature}` : ''}
                                    ${analogIn !== undefined ? ` - Analog: ${analogIn}` : ''}
                                    ${doorCode !== undefined ? ` - Door: ${doorCode}` : ''}
                                    ${sosCode !== undefined ? ` - SOS: ${sosCode}` : ''}
                                    ${mccMnc ? ` - Gsm: ${mccMnc}:${gsm}` : ''}
                                    ${extBat ? ` - Ext: ${extBat}V` : ''} ${intBat ? ` - Int: ${intBat}V` : ''}
                                    <br>
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    <button class="replay-button"><i class="material-icons-outlined">play_arrow</i></button>
                                    <button class="reports-button"><i class="material-icons-outlined">description</i></button>
                                    ${fuelLevel ? `<button class="fuel-button"><i class="material-icons-outlined">local_gas_station</i></button>` : ''}
                                    ${temperature ? `<button class="temperature-button"><i class="material-icons-outlined">device_thermostat</i></button>` : ''}
                                `;

                                // Add click listener for replay button
                                existingItem.querySelector('.replay-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const replayImei = imei;
                                    const replayPort = port;
                                    const replayName = name;
                                    const replayCalibration = calibration;
                                    window.open(`/replay?imei=${replayImei}&port=${replayPort}&name=${replayName}&calibration=${replayCalibration}`, '_blank'); // open in a new tab
                                });

                                // Add click listener for reports button
                                existingItem.querySelector('.reports-button').addEventListener('click', function () {
                                    event.stopPropagation(); // Prevent marker list from collapsing
                                    const reportsImei = imei;
                                    const reportsPort = port;
                                    const reportsName = name;
                                    const reportsCalibration = calibration;
                                    window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                });

                                // Add mouseover listener for fuel button
                                const fuelButton = existingItem.querySelector('.fuel-button');
                                if (fuelButton) {
                                    fuelButton.removeEventListener('mouseover', showFuelChart);
                                    fuelButton.addEventListener('mouseover', function (event) {
                                        showFuelChart(event, imei);
                                    });

                                    // Add click listener for fuel button
                                    existingItem.querySelector('.fuel-button').addEventListener('click', function () {
                                        event.stopPropagation(); // Prevent marker list from collapsing
                                        const reportsImei = imei;
                                        const reportsPort = port;
                                        const reportsName = name;
                                        const reportsCalibration = calibration;
                                        window.open(`/reports?imei=${reportsImei}&port=${reportsPort}&name=${reportsName}&calibration=${reportsCalibration}`, '_blank');
                                    });
                                }

                                // Add mouseover listener for temperature button
                                const temperatureButton = existingItem.querySelector('.temperature-button');
                                if (temperatureButton) {
                                    temperatureButton.removeEventListener('mouseover', showFuelChart);
                                    temperatureButton.addEventListener('mouseover', function (event) {
                                        showTemperatureChart(event, imei);
                                    });
                                }
                            }
                        } else {
                            offlineCount++;
                            // Update marker list UI of online markers without gpsTimestamp in the fragment
                            const existingItem = markerList.querySelector(`[data-imei="${imei}"]`);
                            if (!existingItem) {
                                const listItem = document.createElement('div');
                                listItem.className = 'marker-list-item';
                                listItem.setAttribute('data-imei', imei);
                                listItem.innerHTML = `
                                    <div class="marker-title">
                                    <b>${name}</b><br>
                                    Imei: ${imei}<br>
                                    Status: <span style="color: ${isConnected ? 'blue' : 'gray'};">no GPS data - ${isConnected ? 'Connected' : 'Disconnected'}</span>
                                    </div>
                                    <div class="message-details" style="display: none;">
                                    ${brand ? `Brand: ${brand}<br>` : ''}
                                    <button class="open-button"><i class="material-icons-outlined">terminal</i></button>
                                    </div>
                                `;
                                fragment.appendChild(listItem); // Append to fragment instead of directly to DOM
                            }
                        }
                    }

                    markerList.appendChild(fragment); // Append all updates at once

                    batchIndex++;
                    if (batchIndex * batchSize < data.length) {
                        setTimeout(processBatch, 0); // Schedule the next batch
                    } else {
                        finalizeUpdate(); // Finalize the update once all batches are processed
                    }
                }

                function finalizeUpdate() {
                    // Update count container
                    document.getElementById('allCount').textContent = allCount;
                    document.getElementById('onlineCount').textContent = onlineCount;
                    document.getElementById('offlineCount').textContent = offlineCount;
                    document.getElementById('delayCount').textContent = delayCount;
                    document.getElementById('runningCount').textContent = runningCount;
                    document.getElementById('idlingCount').textContent = idlingCount;

                    spinner.style.display = 'none'; // Hide spinner after fetching is complete
                    isUpdating = false;

                    setTimeout(schedulePlotAllLocations, 60 * 1000); // Schedule every 30 seconds
                }

                processBatch();
            }

            function schedulePlotAllLocations() {
                if ('requestIdleCallback' in window) {
                    requestIdleCallback(() => plotAllLocations());
                } else {
                    // Fallback for browsers that don't support requestIdleCallback
                    requestAnimationFrame(() => plotAllLocations());
                }
            }
        });

        // icons
        const icon_offline = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 472.43 230" xmlns:v="https://vecta.io/nano"><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#000047"/></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#0007ee"/></linearGradient><linearGradient id="C" y2="159.14" spreadMethod="reflect" gradientUnits="userSpaceOnUse" y1="159.14" x2="387.871" x1="374.818"><stop offset="0" stop-color="#7f7f7f"/><stop offset="1" stop-color="#191919"/></linearGradient><linearGradient id="D" y2="620.63" href="#A" y1="792.88" gradientTransform="matrix(2.7566 0 0 .36277 -16.25 -47.5)" x2="107.57" x1="152.1"/><linearGradient id="E" y2="91.491" href="#A" x2="107.56" gradientTransform="matrix(2.7564 0 0 .3628 -16.25 -47.5)" y1="263.75" x1="152.09"/><radialGradient id="F" href="#B" cy="148.78" cx="532.18" gradientTransform="scale(.70711 1.4142)" r="26.946"/><radialGradient id="G" href="#B" cy="78.647" cx="532.12" gradientTransform="scale(.70711 1.4142)" r="32.632"/><radialGradient id="H" href="#A" cy="109.49" cx="563.96" gradientTransform="matrix(.66871 0 0 1.4954 -16.25 -47.5)" r="116.01"/><radialGradient id="I" href="#B" spreadMethod="reflect" cy="73.641" cx="44.298" r="14.706"/><path id="J" d="M58.125 73.749a14.375 14.375 0 1 1-28.75 0 14.375 14.375 0 1 1 28.75 0z"/></defs><path d="M2.5 227.5h441.25c12.29-1.25 17.08-9.38 22.5-24.38 5.21-16.87 4.79-155.62-.62-170.62-4.8-13.125-9.59-30-21.88-30H2.5v225z" stroke-linejoin="round" fill-rule="evenodd" stroke="#000" stroke-width="5" fill="#fff"/><g transform="matrix(1.5 0 0 1.12036 -258.724 -66.838)"><path d="M362.5 87.5h25v50h-25z" fill="url(#G)"/><path d="M362.5 137.5h25v50h-25z" fill-rule="evenodd" fill="url(#C)"/><path d="M362.5 187.5h25v50h-25z" fill="url(#F)"/></g><path d="M348.77 29.655c-3.69.697-6.93 2.229-7.07 5.852 3.83 54.073 3.68 105.7-1.77 159.77 0 3.35 3.09 5.9 5.3 5.9l61.87 6.57c4.71.44 9.57-3.25 10.61-7.53 5.33-55.95 5.66-114.27-.88-171.4.14-4.177-5.05-8.007-9.73-6.689l-58.33 7.524z" stroke="#000" stroke-width="2.432" fill="url(#H)"/><text transform="matrix(0 .9064 1.1033 -.00075 0 0)" y="409.644" x="50.297" font-size="24.272" font-family="Arial Black" fill="red"><tspan x="50.297" y="409.644">AMBULANS</tspan></text><g fill-rule="evenodd"><path d="M242.096 115.917c0 45.218-36.657 81.875-81.875 81.875s-81.875-36.657-81.875-81.875 36.657-81.875 81.875-81.875 81.875 36.657 81.875 81.875z" fill="red"/><g fill="#fff"><path d="M140.001 55.817H180v120h-39.999z"/><path d="M220.001 95.817v39.999h-120V95.817z"/></g><path d="M456.03 196.02c9.01 0 8.29-63.28 8.12-77.5.29-15.09 1.32-85.677-7.5-85.623l-.62 163.12z" fill="#818181"/></g><g stroke="#000" fill="none"><path d="M11.603 16.422h255.849" stroke-width="1.452"/><path d="M11.603 215.362h255.849" stroke-width="1.089"/></g><g fill-rule="evenodd"><g fill="url(#I)"><use href="#J" transform="translate(-16.25 -38.437)"/><use href="#J" transform="translate(-16.25 123.878)"/></g><g stroke="#000" stroke-linejoin="bevel" stroke-width="2.5"><path d="M329.73 25.466c-6.08.625-44.98 1.875-46.85 0-1.87-2.258-1.87-10.611 0-11.932l113.25-.857c7.72-.081 7.79 3.433 4.41 4.446-6.25 1.719-64.13 7.906-70.81 8.343z" fill="url(#E)"/><path d="M329.79 205.61c-6.08-.62-44.98-1.87-46.85 0-1.87 2.26-1.87 10.61 0 11.94l113.25.85c7.72.08 7.79-3.43 4.41-4.44-6.25-1.72-64.13-7.91-70.81-8.35z" fill="url(#D)"/></g></g></svg>');
        const icon_delay = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.6}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#ebebeb" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');
        const icon_running = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#ff6114" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');
        const icon_stopped = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#ca0000" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');
        const icon_ignition_off = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#40e0d0" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');
        const icon_freezer = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 960 476" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill-opacity:.996}.C{opacity:.9}.D{stroke:#191919}.E{stroke-width:5}.F{stroke-linejoin:round}.G{stroke-linecap:round}]]></style><defs><linearGradient id="A" gradientUnits="userSpaceOnUse"></linearGradient><linearGradient id="B" y2="832.028" href="#A" x2="856.469" y1="839.648" x1="879.639"/><linearGradient id="C" y2="841.736" href="#A" x2="805.85" y1="845.31" x1="866.381"/><linearGradient id="D" y2="834.28" href="#A" x2="828.675" y1="839.87" x1="892.755"/><linearGradient id="E" y2="617.32" href="#A" x2="856.469" y1="609.7" x1="879.639"/><linearGradient id="F" y2="623.08" href="#A" x2="897.578" y1="623.14" x1="909.338"/><linearGradient id="G" y2="615.12" href="#A" x2="828.675" y1="609.53" x1="892.755"/><linearGradient id="H" y2="616.13" href="#A" x2="905.198" y1="609.1" x1="918.658"/><linearGradient id="I" y2="650.42" href="#A" x2="857.428" y1="599.45" x1="902.148"/><linearGradient id="J" y2="614.689" href="#A" x2="813.538" y1="615.805" x1="887.335"/><linearGradient id="K" y2="1118.528" href="#A" x2="805.85" y1="93.126" x1="866.381"/><linearGradient id="L" y2="867.68" href="#A" x2="205.59" gradientTransform="scale(1 -1) rotate(7.932 10770.938 311.695)" y1="873.14" x1="229.7"/><linearGradient id="M" y2="872.65" href="#A" x2="216.56" gradientTransform="scale(1 -1) rotate(7.932 10766.693 386.342)" y1="873.06" x1="238.83"/><linearGradient id="N" y2="826.33" href="#A" x2="897.578" y1="826.27" x1="909.338"/><linearGradient id="O" y2="833.28" href="#A" x2="905.198" y1="840.31" x1="918.658"/><linearGradient id="P" y2="798.99" href="#A" x2="857.428" y1="849.96" x1="902.148"/><linearGradient id="Q" y2="834.681" href="#A" x2="813.538" y1="833.565" x1="887.335"/><linearGradient id="R" y2="853.863" href="#A" x2="229.944" y1="862.598" x1="253.069"/><linearGradient id="S" y2="861.581" href="#A" x2="250.383" y1="865.061" x1="272.384"/><path id="T" d="m869.97 817.84-4.437 2.344c.989 1.157 1.795 2.428 2.375 3.844 4.797 11.717-10.736 29.236-26.875 35.78-.517.21-1.813.841-3.407 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><path id="U" d="m901.65 807.69-6.187 1.844c.96 1.713 1.654 3.532 2.03 5.469 3.12 16.034-20.961 34.284-43.03 38.5-3.395.648-28.884 8.576-32.158 8.804v4.125l41.439-12.148c26.285-5.496 44.949-22.448 41.875-38.25-.596-3.062-1.956-5.86-3.969-8.344z"/><path id="V" d="m869.97 631.55-4.437-2.344c.989-1.157 1.795-2.428 2.375-3.844 4.797-11.717-10.736-29.236-26.875-35.78-.517-.21-1.813-.841-3.407-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><path id="W" d="m901.65 641.7-6.187-1.844c.96-1.713 1.654-3.532 2.03-5.469 3.12-16.034-20.961-34.284-43.03-38.5-3.395-.648-28.884-8.576-32.158-8.804v-4.125l41.439 12.148c26.285 5.496 44.949 22.448 41.875 38.25-.596 3.062-1.956 5.86-3.969 8.344z"/></defs><path d="M557.583 7c-1.509.009-4.721.305-6.469.938l-3.5 1.5 8.656 35.938-124.81.28c-2.436.006-4.887-.013-7.343-.03a931.076 931.076 0 0 1-14.844-.22l-22.062-.718-1.188-.062-45.03-2.438-60.125-3.875-42.375-2c-9.369-.289-18.464-.456-27.25-.406l-12.719.219c-4.15.125-8.38.355-12.656.656l-6.437.5c-6.469.568-13.045 1.321-19.594 2.187h-.031a551.944 551.944 0 0 0-13.062 1.907l-12.937 2.156-12.594 2.375c-.01.002-.022-.002-.032 0l-6.093 1.219a836.448 836.448 0 0 0-17.5 3.78c-.01.003-.021-.001-.031 0a895.26 895.26 0 0 0-10.72 2.5 913.018 913.018 0 0 0-18.53 4.657 739.573 739.573 0 0 0-18.094 5L46.4 64.188l-13.687 3.75c-.902.249-1.778.693-2.625 1.312a10.88 10.88 0 0 0-.844.688c-1.1.98-2.15 2.27-3.156 3.844-.004.007.005.024 0 .03-.5.786-1.022 1.641-1.5 2.563-.004.008.004.024 0 .032a47.081 47.081 0 0 0-1.406 2.968c-.004.01.004.023 0 .032-3.667 8.51-6.62 21.13-8.937 36.219-.002.01.001.02 0 .03-.036.235-.059.484-.094.72a412.608 412.608 0 0 0-1.438 10.344l-.093.718a538.908 538.908 0 0 0-1.375 12.47v.03l-.625 6.47v.03l-.563 6.563v.031a810.337 810.337 0 0 0-.969 13.5v.031c-.872 13.684-1.46 27.79-1.78 41.562v.032l-.313 26.907.03 2.937-.03 2.938.312 26.906v.03c.322 13.775.909 27.879 1.781 41.563v.031l.969 13.5V323l.562 6.563v.03l.625 6.47v.03l1.375 12.47.094.719 1.438 10.344c.035.235.058.484.093.718.002.01-.001.022 0 .032 2.318 15.087 5.27 27.709 8.938 36.219.004.009-.004.022 0 .03a47.141 47.141 0 0 0 1.406 2.97c.004.008-.004.023 0 .03.478.923 1 1.779 1.5 2.563.005.007-.005.025 0 .032 1.005 1.573 2.057 2.864 3.156 3.843.282.252.556.477.844.688.847.62 1.723 1.064 2.625 1.312l13.687 3.75 3.812 1.125a737.896 737.896 0 0 0 18.094 5 911.514 911.514 0 0 0 18.531 4.657l10.72 2.5c.01.002.02-.002.03 0a835.757 835.757 0 0 0 17.5 3.78l6.094 1.22c.01.002.022-.002.032 0l12.594 2.375a638.93 638.93 0 0 0 12.937 2.156l13.062 1.906h.03c6.55.866 13.126 1.619 19.595 2.187l6.437.5c4.276.301 8.505.531 12.656.657l12.72.218c8.785.05 17.88-.117 27.25-.406 13.641-.42 27.846-1.135 42.374-2l60.124-3.875 45.031-2.437 1.188-.063 22.062-.719 14.844-.218 7.343-.032 124.81.282-8.656 35.938 3.5 1.5c1.748.632 4.96.928 6.469.937a14.11 14.11 0 0 0 2.687-.25c.311-.058.624-.14.938-.219.304-.075.632-.156.937-.25a17.917 17.917 0 0 0 1.875-.687c1.822-.794 3.535-1.931 4.782-3.313l.03-.03a8.311 8.311 0 0 0 1.47-2.282l12.155-31.312 109.94.25 2.22 1.094a93.427 93.427 0 0 0 8.843 3.625 92.16 92.16 0 0 0 4.375 1.406c5.1 1.508 10.188 2.552 15.344 3.25 2.946.4 5.926.694 8.937.906a187.11 187.11 0 0 0 4.563.25c3.057.14 6.138.213 9.312.25h20c10.41 0 20.322-.53 29.781-1.562a238.31 238.31 0 0 0 11.125-1.469 210.889 210.889 0 0 0 20.906-4.375l4.969-1.375a180.69 180.69 0 0 0 14.187-4.781l4.53-1.781a164.843 164.843 0 0 0 8.72-3.938l4.218-2.125a155.594 155.594 0 0 0 8.094-4.562 151.395 151.395 0 0 0 3.875-2.438c6.394-4.12 12.363-8.625 17.906-13.53a147.25 147.25 0 0 0 12.5-12.47c.005-.006-.005-.025 0-.031l2.875-3.313c.005-.006-.005-.025 0-.03l2.781-3.407c.005-.006-.005-.025 0-.031a155.237 155.237 0 0 0 7.75-10.72c.005-.006-.005-.023 0-.03l2.406-3.72c.005-.007-.005-.023 0-.03 2.34-3.779 4.567-7.653 6.625-11.656.004-.008-.004-.024 0-.032a177.11 177.11 0 0 0 2-4.03c.004-.009-.004-.024 0-.032l1.938-4.094c.004-.009-.004-.023 0-.031a187.592 187.592 0 0 0 3.562-8.406c.004-.01-.004-.023 0-.031l1.625-4.313c.003-.009-.003-.022 0-.031a203.141 203.141 0 0 0 3.031-8.813c.003-.009-.003-.022 0-.03l1.375-4.5c.003-.01-.003-.023 0-.032l1.281-4.563c.003-.01-.003-.021 0-.03a235.721 235.721 0 0 0 3.344-14.095v-.03l.906-4.813v-.032c2.075-11.318 3.463-23.062 4.157-35.219v-.03l.25-5.22v-.03l.28-10.563v-.031-5.375c0-.636-.021-1.271-.03-1.906l.03-1.906v-5.375-.032a337.906 337.906 0 0 0-.28-10.562v-.03l-.25-5.22v-.03c-.694-12.158-2.082-23.9-4.157-35.22v-.031l-.906-4.813v-.03a235.867 235.867 0 0 0-3.344-14.095c-.003-.01.003-.021 0-.03l-1.28-4.563c-.004-.01.002-.022 0-.032l-1.376-4.5c-.003-.009.003-.022 0-.03l-1.468-4.438-1.563-4.375c-.003-.01.003-.023 0-.032a197.25 197.25 0 0 0-1.625-4.312c-.004-.009.004-.022 0-.031a187.377 187.377 0 0 0-3.562-8.406c-.004-.008.004-.023 0-.032-.622-1.376-1.286-2.74-1.938-4.093-.004-.008.004-.024 0-.032a176.408 176.408 0 0 0-2-4.03c-.004-.009.004-.024 0-.032-2.058-4.004-4.284-7.878-6.625-11.656-.005-.007.005-.023 0-.031-.777-1.255-1.597-2.49-2.406-3.719-.005-.007.005-.024 0-.031-2.437-3.7-5.024-7.26-7.75-10.72-.005-.005.005-.025 0-.03a152.01 152.01 0 0 0-2.781-3.407c-.005-.006.005-.025 0-.03a148.56 148.56 0 0 0-2.875-3.313c-.005-.006.005-.026 0-.032a147.136 147.136 0 0 0-12.5-12.469c-5.543-4.905-11.512-9.41-17.906-13.53a151.479 151.479 0 0 0-3.875-2.438 155.594 155.594 0 0 0-8.094-4.563c-1.383-.724-2.8-1.434-4.219-2.125a164.798 164.798 0 0 0-8.718-3.937c-1.49-.62-3.007-1.196-4.531-1.781a180.747 180.747 0 0 0-14.187-4.781l-4.969-1.375a210.847 210.847 0 0 0-20.906-4.375 237.29 237.29 0 0 0-11.125-1.47c-9.459-1.032-19.371-1.563-29.781-1.563-7.02 0-13.652-.074-20 0l-9.312.25-4.563.25a139.71 139.71 0 0 0-8.937.906c-5.156.699-10.244 1.743-15.344 3.25a93.14 93.14 0 0 0-4.375 1.406 93.405 93.405 0 0 0-8.844 3.625c-.742.35-1.472.72-2.219 1.094l-109.94.25-12.156-31.312a8.311 8.311 0 0 0-1.468-2.281c-.01-.01-.022-.02-.031-.031-1.247-1.381-2.96-2.52-4.782-3.313a17.908 17.908 0 0 0-1.875-.687 18.3 18.3 0 0 0-.937-.25c-.314-.078-.627-.16-.938-.219a14.11 14.11 0 0 0-2.687-.25z" stroke-width="14" class="B D"/><path fill="#4e48fa" d="M557.583 7c-1.509.009-4.721.306-6.469.938l-3.5 1.5 8.656 35.843-124.81.282c-77.963.165-166.52-11.504-232.93-9.5s-152.12 28-152.12 28l-13.687 3.78c-19.25 5.297-25.718 97.368-25.718 166.78l.032 3.376-.032 3.375c0 69.414 6.468 161.48 25.718 166.78l13.687 3.781s85.711 25.996 152.12 28 154.97-9.665 232.93-9.5l124.81.281-8.656 35.844 3.5 1.5c1.748.632 4.96.929 6.469.938a14.11 14.11 0 0 0 2.688-.25c.31-.058.623-.141.937-.219.305-.075.633-.157.937-.25a17.946 17.946 0 0 0 1.875-.687c1.822-.792 3.535-1.903 4.782-3.282l.03-.03a8.316 8.316 0 0 0 1.47-2.282l12.156-31.25 109.94.25c23.9 11.942 45.51 10.719 73.593 10.719 133.25 0 187.63-86.586 187-201.38l-.031-2.344.03-2.344c.629-114.79-53.748-201.38-187-201.38-28.081 0-49.692-1.223-73.592 10.72l-109.94.25-12.156-31.25a8.316 8.316 0 0 0-1.47-2.282l-.03-.03c-1.247-1.38-2.96-2.49-4.782-3.282a17.94 17.94 0 0 0-1.875-.688 18.437 18.437 0 0 0-.937-.25c-.314-.077-.627-.16-.937-.218a14.135 14.135 0 0 0-2.688-.25z" class="B"/><path d="M347.403 368.55c-33.364 0-65.307 1.8-94.811 5.063 25.66 48.714 97.985 30.265 205.56 31.53l121.53 2.376c-47.16-23.334-133.53-38.97-232.28-38.97z" fill="#262626" class="B C"/><path opacity=".5" d="M347.403 368.55l-9.562.063c.818 16.17 6.428 30.257 14.594 38.844l14.437-.344c-8.566-8.193-14.593-22.228-15.72-38.562-1.25-.005-2.494 0-3.75 0z" class="B"/><path d="M936.083 340.81l-5.094.594c-21.545 2.512-37.688 25.979-39.28 54.53l-.376 7.126 5.25-4.844c15.89-14.68 28.303-32.507 37.406-52.75l2.09-4.65z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 351.81s79.677-22.596 105.38-31.982c26.839-9.802 98.859-39.146 98.859-39.146s-8.74 42.47-30.483 57.918c-77.23 54.87-232.69 53.85-232.69 53.85" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#T" fill="url(#B)"/><path fill="url(#N)" d="m878.55 813.38-4.438 2.344c.99 1.157 1.796 2.428 2.375 3.844 4.798 11.717-10.736 29.236-26.875 35.78-.516.21-1.812.841-3.406 1.657l13.625-3.875c17.306-8.458 27.47-23.082 23-34-.916-2.238-2.375-4.166-4.28-5.75z"/><use href="#T" x="14.77" y="-5.88" fill="url(#D)"/><use href="#U" fill="url(#O)"/><use href="#U" fill="url(#P)"/><path fill="url(#Q)" d="m857.12 822.46-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.009 31.964-.461.188-1.619.751-3.042 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#C)" d="m843.32 826.03-3.964 2.094a12.633 12.633 0 0 1 2.122 3.433c4.286 10.467-9.591 26.117-24.008 31.964-.462.188-1.62.751-3.043 1.48l12.17-3.462c15.46-7.555 24.54-20.62 20.547-30.373-.819-1.998-2.122-3.721-3.825-5.136z"/><path fill="url(#R)" d="M233.27 845.72c8.293-2.023 15.486-1.479 19.797 5.787l-2.493 17.897c-6.876 6.173-13.75 4.951-20.625.156l3.32-23.84z"/><path fill="url(#S)" d="M253.54 848.99c8.15-1.21 15.167-.573 18.843 5.508l-2.373 17.034c-6.484 2.975-12.983 5.21-19.631.148l3.161-22.69z"/></g><path d="M347.403 366.06c-33.454 0-65.492 1.79-95.093 5.063l-3.656.406 1.718 3.25c6.672 12.664 16.562 21.113 29.062 26.438s27.572 7.612 45.093 8.437c35.042 1.65 79.954-2.631 133.59-2l121.53 2.375 1.125-4.75c-47.84-23.68-134.34-39.22-233.36-39.22zm0 5c91.169 0 171.75 13.479 220.09 33.719l-109.31-2.125c-53.937-.635-98.976 3.652-133.4 2.031-17.214-.81-31.767-3.105-43.406-8.062-10.453-4.452-18.485-11.154-24.5-20.906 28.307-2.983 58.735-4.656 90.53-4.656z" fill="#191919" class="C"/><g fill="#262626" class="B"><path d="M207.563 120.69l-77.749 12.469c-27.15 4.354-48.947 48.773-50.999 104.84 2.052 56.071 23.849 100.49 50.999 104.84l77.749 12.469a23.95 23.95 0 0 0 24-24v-186.62a23.95 23.95 0 0 0-24-24zm431.46-34.22c-2.97 0-5.893.332-8.781.969l-.031-.031-63.843 12.312c-17.728 6.604-32 14.272-32 32v212.56c0 17.728 14.272 25.395 32 32l63.843 12.312.03-.032c2.889.637 5.813.97 8.782.97 45.395 0 82.198-57.364 82.312-151.53-.114-94.17-36.916-151.53-82.312-151.53z" class="C D E"/><path d="M347.403 107.46c-33.364 0-65.307-1.8-94.811-5.063 25.66-48.714 97.985-30.265 205.56-31.53l121.53-2.376c-47.16 23.334-133.53 38.97-232.28 38.97z" class="C"/></g><path opacity=".5" d="M347.403 107.46c-3.206 0-6.383-.03-9.562-.063.818-16.17 6.428-30.257 14.594-38.844l14.437.344c-8.566 8.193-14.593 22.228-15.72 38.562h-3.75z" class="B"/><path d="M936.083 135.2l-5.094-.594c-21.545-2.512-37.688-25.979-39.28-54.53l-.376-7.126 5.25 4.844c15.89 14.68 28.303 32.507 37.406 52.75l2.094 4.656z" fill="#212121" class="D E"/><path opacity=".5" d="M730.533 124.2s79.677 22.596 105.38 31.982c26.839 9.802 98.859 39.146 98.859 39.146s-8.74-42.47-30.483-57.918c-77.23-54.87-232.69-53.86-232.69-53.86" stroke="#292929" stroke-width="6" fill="none" class="F G"/><g transform="translate(-52.937 -486.69)"><use href="#V" fill="url(#E)"/><path fill="url(#F)" d="m878.55 636.01-4.438-2.344c.99-1.157 1.796-2.428 2.375-3.844 4.798-11.717-10.736-29.236-26.875-35.78-.516-.21-1.812-.841-3.406-1.657l13.625 3.875c17.306 8.458 27.47 23.082 23 34-.916 2.238-2.375 4.166-4.28 5.75z"/><use href="#V" x="14.77" y="5.87" fill="url(#G)"/><use href="#W" fill="url(#H)"/><use href="#W" fill="url(#I)"/><path fill="url(#J)" d="m857.12 626.93-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.009-31.964-.461-.188-1.619-.751-3.042-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#K)" d="m843.32 623.36-3.964-2.094a12.633 12.633 0 0 0 2.122-3.433c4.286-10.467-9.591-26.117-24.008-31.964-.462-.188-1.62-.751-3.043-1.48l12.17 3.462c15.46 7.555 24.54 20.62 20.547 30.373-.819 1.998-2.122 3.721-3.825 5.136z"/><path fill="url(#L)" d="M233.27 603.66c8.293 2.023 15.486 1.479 19.797-5.787l-2.493-17.897c-6.876-6.173-13.75-4.951-20.625-.156l3.32 23.84z"/><path fill="url(#M)" d="M253.54 600.4c8.15 1.21 15.167.573 18.843-5.508l-2.373-17.034c-6.484-2.975-12.983-5.21-19.631-.148l3.161 22.69z"/></g><path d="M347.403 109.95c-33.454 0-65.492-1.79-95.093-5.063l-3.656-.406 1.718-3.25c6.672-12.664 16.562-21.113 29.062-26.438s27.572-7.612 45.093-8.437c35.042-1.65 79.954 2.63 133.59 2l121.53-2.375 1.125 4.75c-47.849 23.675-134.36 39.219-233.37 39.219zm0-5c91.169 0 171.75-13.479 220.09-33.719l-109.31 2.125c-53.937.635-98.976-3.652-133.4-2.031-17.214.81-31.767 3.105-43.406 8.062-10.453 4.452-18.485 11.154-24.5 20.906 28.307 2.983 58.735 4.656 90.53 4.656z" fill="#191919" class="C"/></svg>');

    </script>
</body>

</html>
