
// npm install express

// http://dev.inavcloud.com:3006/api/messages_temperature?imei=863406070011973&startDate=2024-10-24%2016:00:00&endDate=2024-10-25%2015:59:59

// http://dev.inavcloud.com:3006/api/messages?imei=864714067568064&startDate=2025-03-04%2016:00:00&endDate=2025-03-05%2015:59:59
// http://dev.inavcloud.com:3006/api/messages/list?imeis=352503096732844,66434286&startDate=2025-03-04%2016:00:00&endDate=2025-03-05%2015:59:59
// http://dev.inavcloud.com:3006/api/messages/last?imei=864714067571001
// http://dev.inavcloud.com:3006/api/messages/last/list?imeis=352503096732844,66434286
// http://dev.inavcloud.com:3006/api/messages/last/all
// http://dev.inavcloud.com:3006/api/ibutton/last
// http://dev.inavcloud.com:3006/api/sos/all?startDate=2024-10-14%2016:00:00&endDate=2024-10-15%2015:59:59
// http://dev.inavcloud.com:3006/api/alarm/last
// http://dev.inavcloud.com:3006/api/alarm?imei=866496072204863&startDate=2025-05-20%2016:00:00&endDate=2025-05-21%2015:59:59

// http://dev.inavcloud.com:3006/api/send-command?imei=352503096734139&command=status
// http://dev.inavcloud.com:3006/api/device/raw?imei=352625696390440


const express = require('express');
const router = express.Router();
const { getDbInstance, imeiLastRecords } = require('./db_2go.js');
const db = getDbInstance();


// API endpoint to search for TEMPERATURE messages by IMEI and date range
router.get('/messages_temperature', (req, res) => {
    const { imei, startDate, endDate } = req.query;

    if (!imei || !startDate || !endDate) {
        return res.status(400).json({ error: 'IMEI, startDate, and endDate are required' });
    }

    // Sanitize IMEI to prevent SQL injection
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');

    // Validate and parse the dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Check if date parsing was successful
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ error: 'Invalid date format. Use ISO 8601 format.' });
    }

    // Convert to YYYY-MM-DD HH:MM:SS format for SQL
    const startUTC = start.toISOString().slice(0, 19).replace('T', ' ');
    const endUTC = end.toISOString().slice(0, 19).replace('T', ' ');

    // Generate the dynamic table name for the IMEI
    const tableName = `gps_data_${sanitizedImei}`;

    // Construct the query and parameters
    const query = `
        SELECT gpsTimestamp, speed, temperature FROM ${tableName}
        WHERE gpsTimestamp BETWEEN ? AND ?
        ORDER BY gpsTimestamp
    `;

    const queryParams = [startUTC, endUTC];

    // Execute the query with the parameters
    db.all(query, queryParams, (err, rows) => {
        if (err) {
            console.error('Error executing query', err.message);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
        res.json(rows);
    });
});

// API endpoint to search for messages by IMEI and date range
router.get('/messages', (req, res) => {
    const { imei, startDate, endDate } = req.query;

    if (!imei || !startDate || !endDate) {
        return res.status(400).json({ error: 'IMEI, startDate, and endDate are required' });
    }

    // Sanitize IMEI to prevent SQL injection
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');

    // Validate and parse the dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Check if date parsing was successful
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ error: 'Invalid date format. Use ISO 8601 format.' });
    }

    // Convert to YYYY-MM-DD HH:MM:SS format for SQL
    const startUTC = start.toISOString().slice(0, 19).replace('T', ' ');
    const endUTC = end.toISOString().slice(0, 19).replace('T', ' ');

    // Generate the dynamic table name for the IMEI
    const tableName = `gps_data_${sanitizedImei}`;

    // Construct the query and parameters
    const query = `
        SELECT * FROM ${tableName}
        WHERE gpsTimestamp BETWEEN ? AND ?
    `;
    // ORDER BY gpsTimestamp

    const queryParams = [startUTC, endUTC];

    // Execute the query with the parameters
    db.all(query, queryParams, (err, rows) => {
        if (err) {
            console.error('Error executing query', err.message);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
        res.json(rows);
    });
});

// API endpoint to search for messages by a list of IMEIs and date range
router.get('/messages/list', async (req, res) => {
    const { imeis, startDate, endDate } = req.query;

    if (!imeis || !startDate || !endDate) {
        return res.status(400).json({ error: 'IMEIs, startDate, and endDate are required' });
    }

    const imeiArray = imeis.split(',');

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ error: 'Invalid date format. Use ISO 8601 format.' });
    }

    const startUTC = start.toISOString().slice(0, 19).replace('T', ' ');
    const endUTC = end.toISOString().slice(0, 19).replace('T', ' ');

    try {
        const queryPromises = imeiArray.map((imei) => {
            const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');
            const tableName = `gps_data_${sanitizedImei}`;
            const query = `
                SELECT '${sanitizedImei}' AS imei, * FROM ${tableName}
                WHERE gpsTimestamp BETWEEN ? AND ?
            `;
            const queryParams = [startUTC, endUTC];

            return new Promise((resolve, reject) => {
                db.all(query, queryParams, (err, rows) => {
                    if (err) {
                        reject(`Error querying table ${tableName}: ${err.message}`);
                    } else {
                        resolve(rows);
                    }
                });
            });
        });

        // Wait for all queries to finish
        const resultsArray = await Promise.allSettled(queryPromises);

        // Aggregate successful results and handle errors
        let results = [];
        let errors = [];

        resultsArray.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                if (result.value.length > 0) {
                    results = results.concat(result.value);
                }
            } else {
                errors.push(`IMEI ${imeiArray[index]}: ${result.reason}`);
            }
        });

        if (results.length === 0 && errors.length > 0) {
            return res.status(500).json({ error: 'Some queries failed', details: errors });
        }

        if (results.length === 0) {
            return res.status(404).json({ error: 'No records found for the specified IMEIs' });
        }

        if (errors.length > 0) {
            console.warn('Partial failures:', errors);
        }

        res.json(results);

    } catch (error) {
        console.error('Unexpected error:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// API endpoint to get the last record of a specific IMEI
router.get('/messages/last', (req, res) => {
    const { imei } = req.query;

    if (!imei) {
        return res.status(400).json({ error: 'IMEI is required' });
    }

    const lastRecord = imeiLastRecords.get(imei);

    if (!lastRecord) {
        return res.status(404).json({ error: 'No record found for the provided IMEI' });
    }

    res.json(lastRecord);
});

// API endpoint to get the last record of a list of IMEIs
router.get('/messages/last/list', (req, res) => {
    const { imeis } = req.query
    const allRecords = Array.from(imeiLastRecords.values()); // Convert Map values to an array

    if (!imeis) {
        return res.status(400).json({ error: 'IMEIs are required' });
    }

    // Parse the IMEIs from the query parameter
    const imeiArray = imeis.split(',');
    const lastRecords = []
    imeiArray.forEach(imei => {
        const lastRecord = imeiLastRecords.get(imei)
        if (lastRecord) lastRecords.push(lastRecord)
    })

    if (lastRecords.length === 0) {
        return res.status(404).json({ error: 'No IMEI records found' })
    }

    res.json(lastRecords)
});

// API endpoint to get last record of all IMEIs
router.get('/messages/last/all', (req, res) => {
    const allRecords = Array.from(imeiLastRecords.values()); // Convert Map values to an array

    if (allRecords.length === 0) {
        return res.status(404).json({ error: 'No IMEI records found' });
    }

    res.json(allRecords);
});

// API endpoint to get last record of all iButtons
router.get('/ibutton/last', (req, res) => {
    const query = `
        SELECT * FROM last_records_ibutton
        ORDER BY gpsTimestamp DESC
    `;

    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('Error executing query', err.message);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
        res.json(rows);
    });
});

// API endpoint to search for messages with SOS for all IMEIs and date range
router.get('/sos/all', async (req, res) => {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
        return res.status(400).json({ error: 'startDate and endDate are required' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ error: 'Invalid date format. Use ISO 8601 format.' });
    }

    const startUTC = start.toISOString().slice(0, 19).replace('T', ' ');
    const endUTC = end.toISOString().slice(0, 19).replace('T', ' ');

    try {
        const getTablesQuery = `SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'gps_data_%'`;
        const tables = await new Promise((resolve, reject) => {
            db.all(getTablesQuery, (err, rows) => {
                if (err) {
                    console.error('Error fetching table names', err.message);
                    return reject(err);
                }
                resolve(rows);
            });
        });

        if (tables.length === 0) {
            return res.status(404).json({ message: 'No IMEI tables found.' });
        }

        const allResults = await Promise.all(tables.map(table => {
            const imei = table.name.split('_')[2];
            const query = `
                SELECT ? AS imei, COUNT(*) AS sosCount, MAX(gpsTimestamp) AS lastGpsTimestamp, *
                FROM ${table.name}
                WHERE sosCode = 1
                AND gpsTimestamp BETWEEN ? AND ?
                GROUP BY imei
            `;
            const queryParams = [imei, startUTC, endUTC];

            return new Promise((resolve, reject) => {
                db.all(query, queryParams, (err, rows) => {
                    if (err) {
                        console.error(`Error fetching records from table ${table.name}`, err.message);
                        return reject(err);
                    }
                    resolve(rows);
                });
            });
        }));

        const flattenedResults = allResults.flat();

        // Sort the results by sosCount in descending order
        const sortedResults = flattenedResults.sort((a, b) => b.sosCount - a.sosCount);

        res.json(sortedResults);
    } catch (err) {
        console.error('Error fetching records', err.message);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// API endpoint to get last record of all Alarm
router.get('/alarm/last', (req, res) => {
    const query = `
        SELECT * FROM last_records_alarm
        ORDER BY gpsTimestamp DESC
    `;

    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('Error executing query', err.message);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
        res.json(rows);
    });
});

// API endpoint to search for Alarm messages by IMEI and date range
router.get('/alarm', (req, res) => {
    const { imei, startDate, endDate } = req.query;

    if (!imei || !startDate || !endDate) {
        return res.status(400).json({ error: 'IMEI, startDate, and endDate are required' });
    }

    // Sanitize IMEI to prevent SQL injection
    const sanitizedImei = imei.replace(/[^a-zA-Z0-9_]/g, '');

    // Validate and parse the dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Check if date parsing was successful
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ error: 'Invalid date format. Use ISO 8601 format.' });
    }

    // Convert to YYYY-MM-DD HH:MM:SS format for SQL
    const startUTC = start.toISOString().slice(0, 19).replace('T', ' ');
    const endUTC = end.toISOString().slice(0, 19).replace('T', ' ');

    // Generate the dynamic table name for the IMEI
    const tableName = `gps_data_${sanitizedImei}`;

    // Construct the query and parameters
    const query = `
        SELECT * FROM ${tableName}
        WHERE alarmCode != 'Tracking by Interval'
        AND gpsTimestamp BETWEEN ? AND ?
    `;

    const queryParams = [startUTC, endUTC];

    // Execute the query with the parameters
    db.all(query, queryParams, (err, rows) => {
        if (err) {
            console.error('Error executing query', err.message);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
        res.json(rows);
    });
});


// Helper function to wait for a device to connect with timeout
function waitForDeviceToConnect(imei, imeiToHandler, timeout) {
    return new Promise((resolve) => {
        // If device is already connected, resolve immediately
        if (imeiToHandler.has(imei)) {
            resolve(true);
            return;
        }

        console.log(`Waiting for device ${imei} to connect...`);

        const checkConnectionInterval = setInterval(() => {
            if (imeiToHandler.has(imei)) {
                console.log(`Device ${imei} has connected!`);
                clearInterval(checkConnectionInterval);
                clearTimeout(timeoutId);
                resolve(true);
            }
        }, 500); // Check every 500ms

        const timeoutId = setTimeout(() => {
            clearInterval(checkConnectionInterval);
            console.log(`Timeout waiting for device ${imei} to connect`);
            resolve(false); // Device didn't connect within timeout
        }, timeout);
    });
}

// Command handler
function createCommandHandler(commandAndResponse, imeiToHandler) {
    return async (req, res) => {
        const { imei, command } = req.query;

        // Validate inputs
        if (!imei || !command) {
            return res.status(400).json({ error: 'IMEI and command are required' });
        }

        // Wait for the device to connect if it's not already connected
        const connectionTimeout = 60 * 1000; // 1 minute to wait for reconnection
        const deviceConnected = await waitForDeviceToConnect(imei, imeiToHandler, connectionTimeout);

        if (!deviceConnected) {
            return res.status(504).json({ error: 'Device did not connect within the timeout period' });
        }

        // Create a new command entry in the commandAndResponse map
        commandAndResponse.set(imei, { command, response: null });

        const commandData = commandAndResponse.get(imei);
        commandData.command = command;
        commandData.response = null; // Reset response

        // Wait for the response from the device or timeout
        try {
            const commandResponse = await waitForDeviceResponse(imei, commandAndResponse, 300 * 1000); // 5 minute timeout
            if (commandResponse) {
                return res.json(commandResponse);
            } else {
                return res.status(504).json({ error: 'Device reply timed out' });
            }
        } catch (error) {
            return res.status(500).json({ error: 'Failed to get device response' });
        }
    };
}

// Helper function to wait for a device response with timeout
function waitForDeviceResponse(imei, commandAndResponse, timeout) {
    return new Promise((resolve) => {
        const checkRawDataInterval = setInterval(() => {
            const commandData = commandAndResponse.get(imei);
            if (commandData && commandData.response) {
                clearInterval(checkRawDataInterval);
                clearTimeout(timeoutId);
                resolve(commandData.response);
            }
        }, 100); // Check every 100ms

        const timeoutId = setTimeout(() => {
            clearInterval(checkRawDataInterval);
            resolve(null); // No response, resolve with null
        }, timeout);
    });
}


// Get raw data of a specific IMEI
function createRawDataHandler(imeiToHandler) {
    return async (req, res) => {
        const { imei } = req.query;
        let responseEnded = false;

        if (!imei) {
            return res.status(400).json({ error: 'IMEI is required' });
        }

        if (imeiToHandler.has(imei)) {
            const { handlerType } = imeiToHandler.get(imei);

            res.setHeader('Content-Type', 'text/event-stream');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Connection', 'keep-alive');
            res.flushHeaders();

            // Reduce interval to 1 second to catch more updates
            const intervalId = setInterval(async () => {
                if (responseEnded) return;
                try {
                    const message = await waitForDeviceRawData(imei, imeiToHandler, 2000); // 2 second timeout
                    if (message) {  // Remove duplicate check here since it's handled in waitForDeviceRawData
                        if (!responseEnded) {
                            res.write(`data: ${handlerType} [${imei}] - ${message}\n\n`);
                        }
                    }
                } catch (error) {
                    console.error("Error retrieving device data:", error);
                    clearInterval(intervalId);
                    handleSocketError(error, res, intervalId);
                }
            }, 1000);  // Check every 1 second

            req.on('close', () => {
                clearInterval(intervalId);
                if (!responseEnded) res.end();
                responseEnded = true;
            });
        } else {
            return res.status(404).json({ error: 'Device not connected' });
        }
    };
}

function handleSocketError(error, res, intervalId) {
    if (error.code === 'ECONNRESET') {
        console.log("Socket connection reset. Attempting to clean up.");
    }
    clearInterval(intervalId);
    res.end();
}

function waitForDeviceRawData(imei, imeiToHandler, timeout) {
    return new Promise((resolve, reject) => { // Keep reject as it's used in the catch block
        const checkRawDataInterval = setInterval(() => {
            try {
                if (imeiToHandler.has(imei)) {
                    const { handler } = imeiToHandler.get(imei);
                    if (handler && handler.data) {
                        // Store and check lastData in the handler object
                        if (!handler.lastData || handler.data !== handler.lastData) {
                            handler.lastData = handler.data;
                            clearInterval(checkRawDataInterval);
                            clearTimeout(timeoutId);
                            resolve(handler.data);
                            return;
                        }
                    }
                }
            } catch (error) {
                console.error("Error accessing handler data:", error);
                clearInterval(checkRawDataInterval);
                clearTimeout(timeoutId);
                reject(new Error("Error accessing handler data"));
            }
        }, 100); // Check every 100ms

        const timeoutId = setTimeout(() => {
            clearInterval(checkRawDataInterval);
            resolve(null);
        }, timeout);
    });
}

// Export the router and the handler functions
module.exports = {
    router,
    createCommandHandler,
    createRawDataHandler,
    waitForDeviceToConnect
};