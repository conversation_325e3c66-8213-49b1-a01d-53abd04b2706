// <PERSON>
// Aug 4, 2024

// npm install express
// npm install chart.js

const express = require('express');
const http = require('http');
const path = require('path');

const app = express();
const server = http.createServer(app);

// Add CORS headers to help with cross-origin requests
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Serve static files from node_modules
app.use('/js', express.static(path.join(__dirname, '..', 'node_modules/chart.js/dist')));

// Serve the main HTML page
app.get('/', (req, res) => {
    res.sendFile(__dirname + '/index-map.html');
});

// Serve the replay page and handle the IMEI query parameter
app.get('/replay', (req, res) => {

    const replayImei = req.query.imei;
    const replayPort = req.query.port;

    res.sendFile(__dirname + '/index-replay.html');
});

// Serve the reports page
app.get('/reports', (req, res) => {

    const reportsImei = req.query.imei;
    const reportsPort = req.query.port;

    res.sendFile(__dirname + '/index-reports.html');
});

// Serve the reports page
app.get('/summarize', (req, res) => {

    const summarizePort = req.query.port;

    res.sendFile(__dirname + '/index-summarize.html');
});

server.listen(3000, () => {
    console.log('HTTP Server is running on http://localhost:3000');
});


