
const net = require('net');
const { getDbInstance, createTableForIMEI, updateLastRecord, imeiLastRecords } = require('./db_unilever.js');
const db = getDbInstance();
const fs = require('fs');

const destinations = [
    { name: 'Fleetx', host: '************', port: 4444 },
];

const commandSeqNumImei = new Map();
const lastCommandSentTime = new Map(); // Track when commands were last sent to each IMEI
const imeiErrorCount = new Map(); // Track error counts per IMEI

let destinationConnectionId = 1;
let logCount = 10;
const IDLE_TIMEOUT = 30 * 1000;
const SQL_BATCH_INTERVAL = 10 * 1000;
const COMMAND_COOLDOWN = 120 * 1000; // 2 minutes in milliseconds
const MAX_ERRORS_PER_IMEI = 10; // Maximum number of errors to log per IMEI
const imeiLatLngTimeAcc = new Map();

const options = {
    timeZone: 'Asia/Manila',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
};

// Constants
const START_BIT = '&&';
const COMMAND_HEADER = '$$';

// Message types that need response
const MESSAGE_NEED_RESPONSE_TYPE = [
];

// Load the Alarm ID to name mapping from a text file
const alarmNames = {};
const alarmMappingData = fs.readFileSync('alarm_names.csv', 'utf8');
alarmMappingData.split('\n').forEach(line => {
    const [alarmId, alarmName] = line.trim().split(',');
    alarmNames[alarmId] = alarmName;
});

class Istartek2goHandler {
    constructor() {

        this.destinationSockets = new Map();
        this.sqlBatch = [];
        this.sqlBatchTimer = null;

        this.imei = '';
        this.data = '';
        this.lastData = '';

        this.initializeSQLBatch();
    }

    initializeSQLBatch() {
        this.sqlBatchTimer = setInterval(this.sqlLogBatch.bind(this), SQL_BATCH_INTERVAL);
    }

    async sqlLogBatch() {
        if (this.sqlBatch.length > 0 && this.imei) {
            createTableForIMEI(db, this.imei);  // Ensure table for the IMEI is created

            const sanitizedImei = this.imei.replace(/[^a-zA-Z0-9_]/g, '');
            const query = `INSERT INTO gps_data_${sanitizedImei} (serverTimestamp, gpsTimestamp, latitude, longitude, speed, course, satelliteCount, mccMnc, gsm, inStatus, accCode, sosCode, outStatus, extBat, intBat, temperature, iButton, doorCode, alarmCode) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

            db.serialize(() => {
                db.run("BEGIN TRANSACTION");
                const stmt = db.prepare(query);
                for (const message of this.sqlBatch) {
                    stmt.run(message, (err) => {
                        if (err) {
                            console.error('Error inserting message', err.message);
                        } else {
                            // After inserting, update the last record for the IMEI
                            updateLastRecord(db, this.imei, message);
                        }
                    });
                }
                stmt.finalize();
                db.run("COMMIT");
            });

            // Clear the batch after insertion
            this.sqlBatch = [];
        }
    }

    connectToDestinationSockets() {
        for (const destination of destinations) {
            const socket = new net.Socket();

            socket.connect(destination.port, destination.host, () => {
                socket.destination = destination;
                socket.connectionId = destinationConnectionId++;
                this.destinationSockets.set(socket.connectionId, socket);
                //console.log(`${this.imei} connected to ${destination.name}[${socket.connectionId}]`);
            });

            socket.on('error', err => {
                if (logCount !== 0) {
                    console.log(`Error connecting to ${destination.name}: ${err.message}`);
                    logCount--;
                }
                socket.destroy();
            });

            socket.on('close', () => {
                socket.removeAllListeners();
                this.destinationSockets.delete(socket.connectionId);
            })

            socket.setTimeout(IDLE_TIMEOUT, () => {
                socket.destroy();
            });
        }
    }

    async handle(deviceSocket, data, commandAndResponse) {

        //const serverTimestamp = new Date().toLocaleString('en-CA', options).replace(',', '');
        const serverTimestamp = new Date().toISOString();

        try {
            const parsedMessage = Istartek2goHandler.parseMessage(data);
            //if (parsedMessage.ibutton) console.log('Parsed message:', parsedMessage);

            if (parsedMessage.imei) this.imei = parsedMessage.imei;
            if (parsedMessage.data) this.data = parsedMessage.data;

            //if (this.imei === '866496072214805') console.log(data.toString());

            // Insert LOGIN message into SQLite database
            const updatedData = {
                imei: this.imei,
                serverTimestamp: serverTimestamp,
                gpsTimestamp: parsedMessage.gpsTimestamp,
                latitude: parsedMessage.latitude,
                longitude: parsedMessage.longitude,
                speed: parsedMessage.speed,
                course: parsedMessage.course,
                satelliteCount: parsedMessage.satelliteCount,
                mccMnc: parsedMessage.mccMnc,
                gsm: parsedMessage.gsm,
                inStatus: parsedMessage.inStatus,
                accCode: parsedMessage.accCode,
                sosCode: parsedMessage.sosCode,
                outStatus: parsedMessage.outStatus,
                extBat: parsedMessage.extBat,
                intBat: parsedMessage.intBat,
                temperature: parsedMessage.temperature,
                ibutton: parsedMessage.ibutton,
                doorCode: parsedMessage.doorCode,
                alarmCode: parsedMessage.alarmCode
            };

            imeiLastRecords.set(this.imei, updatedData);

            // Filter out GPS messages
            const latLngTimeAcc = imeiLatLngTimeAcc.get(this.imei) || [0, 0, 0, 0];
            const dist = Math.sqrt(Math.pow(latLngTimeAcc[0] - parsedMessage.latitude, 2) + Math.pow(latLngTimeAcc[1] - parsedMessage.longitude, 2)) * 111 * 1000;
            const timeDiff = (new Date(parsedMessage.gpsTimestamp) - new Date(latLngTimeAcc[2])) / 1000;
            const isAccChanged = (parsedMessage.accCode !== latLngTimeAcc[3]);
            const isDoorChanged = (parsedMessage.doorCode !== latLngTimeAcc[4]);
            const isTemperatureNegative = (parsedMessage.temperature ? parsedMessage.temperature.split('|')[0] < 0 : false);
            const isLatLng = (parsedMessage.latitude !== 0 && parsedMessage.longitude !== 0);
            const isSos = (parsedMessage.sosCode !== 0);
            const isIbutton = (parsedMessage.ibutton !== '');

            // Insert message into SQLite database
            if (parsedMessage.gpsTimestamp && isLatLng && (dist > 50 || timeDiff > 3600 / 6 || isAccChanged || isSos || isIbutton || isDoorChanged || (isTemperatureNegative && timeDiff > 3600 / 6))) {
                this.sqlBatch.push([
                    serverTimestamp,
                    parsedMessage.gpsTimestamp,
                    parsedMessage.latitude,
                    parsedMessage.longitude,
                    parsedMessage.speed,
                    parsedMessage.course,
                    parsedMessage.satelliteCount,
                    parsedMessage.mccMnc,
                    parsedMessage.gsm,
                    parsedMessage.inStatus,
                    parsedMessage.accCode,
                    parsedMessage.sosCode,
                    parsedMessage.outStatus,
                    parsedMessage.extBat,
                    parsedMessage.intBat,
                    parsedMessage.temperature,
                    parsedMessage.ibutton,
                    parsedMessage.doorCode,
                    parsedMessage.alarmCode,
                ]);
                imeiLatLngTimeAcc.set(
                    this.imei,
                    [parseFloat(parsedMessage.latitude),
                    parseFloat(parsedMessage.longitude),
                    parsedMessage.gpsTimestamp,
                    parsedMessage.accCode,
                    parsedMessage.doorCode]
                );
            }

            // Check if destination sockets already exist for this IMEI
            if (this.destinationSockets && this.destinationSockets.size === 0) {
                await new Promise((resolve) => {
                    this.connectToDestinationSockets();

                    // Check if all destination sockets are connected
                    const intervalId = setInterval(() => {
                        if (this.destinationSockets.size === destinations.length) {
                            clearInterval(intervalId);
                            clearTimeout(timeoutId);
                            // Only resolve the promise here, after sockets have connected
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to reject the promise if the condition isn't met in time
                    const timeoutId = setTimeout(() => {
                        clearInterval(intervalId);
                        resolve();
                    }, 5 * 1000)
                });
            }

            // Forward device data and destination response
            if (this.destinationSockets && this.destinationSockets.size > 0) {
                this.destinationSockets.forEach(socket => {

                    // Send device data to destination
                    if (socket && socket.writable) {
                        //console.log(`Send ${this.imei}[${deviceSocket.connectionId}] data to ${socket.destination.name}[${socket.connectionId}] ${data.toString('hex')}`);
                        socket.write(data, err => {
                            if (err) {
                                socket.destroy();
                                this.destinationSockets.delete(socket.connectionId);
                            }
                        });
                    } else {
                        this.destinationSockets.delete(socket.connectionId);
                        return; // Skip to the next socket
                    }

                    // Send destination response to device
                    socket.removeAllListeners('data');
                    socket.on('data', (response) => {
                        if (deviceSocket && deviceSocket.writable) {
                            //console.log(`${this.imei} received response from ${socket.destination.name}[${socket.connectionId}]`);
                            deviceSocket.write(response, err => {
                                if (err) {
                                    deviceSocket.destroy();
                                    //console.log(`Error sending response to ${this.imei} [${deviceSocket.connectionId}]`);
                                }
                            });
                        } else {
                            deviceSocket.destroy();
                            //console.log(`Error ${this.imei} not writable [${deviceSocket.connectionId}]`);
                        }
                    });
                });
            }

            // Send response to device
            if (this.isMessageNeedResponse(parsedMessage)) {
                const responseMessage = this.createResponse(parsedMessage.checksum, parsedMessage.messageType, parsedMessage.sequenceNumber);
                //console.log(`Response to ${this.imei} [${deviceSocket.connectionId}]:`, responseMessage);
                if (deviceSocket && deviceSocket.writable) {
                    deviceSocket.write(responseMessage, (err) => {
                        if (err) {
                            deviceSocket.destroy();
                            console.log(`Error sending my response to ${this.imei}`);
                        }
                    });
                }
                // Add delay before sending command
                await new Promise(resolve => setTimeout(resolve, 100)); // add delay
            }

            // Send command to device
            if (commandAndResponse && commandAndResponse.has(this.imei)) {
                const commandData = commandAndResponse.get(this.imei);

                // Send command if no response yet
                if (!commandData.response) {
                    const currentTime = Date.now();
                    const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                    // Check if the cooldown period has passed since the last command was sent
                    if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                        const commandSeqNum = Istartek2goHandler.incrementSequenceNumber(parsedMessage.sequenceNumber);
                        commandSeqNumImei.set(this.imei, commandSeqNum);
                        const commandMessage = Istartek2goHandler.constructCommandMessage(commandData.command, this.imei, commandSeqNum);
                        console.log(`Send command to ${this.imei} [${deviceSocket.connectionId}]:`, commandMessage);

                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending command to ${this.imei}`);
                                }
                            });

                            // Update the last sent time for this IMEI
                            lastCommandSentTime.set(this.imei, currentTime);
                        }
                    } else {
                        console.log(`Command to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN / 1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                    }
                }
            }

            // Store command response
            if (parsedMessage.responseToCommand) {
                const commandData = commandAndResponse.get(this.imei);
                if (commandData) {
                    commandData.response = parsedMessage.responseToCommand;
                    commandAndResponse.set(this.imei, commandData);
                    commandSeqNumImei.delete(this.imei);
                    console.log(`Response to Command ${this.imei} [${deviceSocket.connectionId}]:`, data.toString(), commandData.response);
                }
            }

        } catch (error) {
            // Get current error count for this IMEI or initialize to 0
            const errorCount = imeiErrorCount.get(this.imei) || 0;

            // Only log if we haven't reached the maximum number of errors for this IMEI
            if (errorCount < MAX_ERRORS_PER_IMEI) {
                console.error(`iStartek Handle Error [${this.imei}]:`, error.message);
                // Increment the error count for this IMEI
                imeiErrorCount.set(this.imei, errorCount + 1);
            }
        }
    }

    cleanupDestinationSockets() {
        if (this.destinationSockets) {
            this.destinationSockets.forEach(socket => {
                socket.removeAllListeners('data');
            });
        }
        if (this.sqlBatchTimer) {
            if (this.sqlBatch.length > 0) {
                this.sqlLogBatch()
                    .then(() => {
                        // Clear the timer only after data has been inserted
                        clearInterval(this.sqlBatchTimer);
                        this.sqlBatchTimer = null;
                    });
            } else {
                clearInterval(this.sqlBatchTimer);
                this.sqlBatchTimer = null;
            }
        }
    }

    cleanup() {
        this.cleanupDestinationSockets();
    }


    // HELPER FUNCTIONS
    static getMessageSegment = (message, start, end) => message.slice(start, end).toString('hex');
    static getAsciiSegment = (message, start, end) => message.slice(start, end).toString('ascii');
    isMessageNeedResponse = (parsedMessage) => MESSAGE_NEED_RESPONSE_TYPE.includes(parsedMessage.messageType);


    // Parse messages
    static parseMessage = (message) => {
        const messageType = message.toString().slice(0, 2);
        const sequenceNumber = message.toString().slice(2, 3);
        const components = message.toString().split(',');
        const imei = components[1];
        const commandSeqNum = commandSeqNumImei.get(imei);

        switch (sequenceNumber) {
            case commandSeqNum:
                return {
                    ...Istartek2goHandler.parseCommandResponse(message),
                    messageType
                }
            default:
                return {
                    ...Istartek2goHandler.parseGpsMessage(message),
                    messageType
                };
            //throw new Error(`iStartek Unilever Unparsed ${message}`);
        }
    };


    // Parse GPS message
    // head_type_len,imei,type2,event,eventData,timestamp,gpsFix,lat,lon,sat,hoop,speed,coarse,altitude,odometer,mcc|mnc|lac|cellId,gsmCsq,status,inStatus,outStatus,extV|batV|adc1|adc2,protocol,fuel,temp,checksum
    // &&V150,865235053670713,000,0,,240418155945,A,14.285806,121.078728,26,0.6,0,8,39,2297949,515|3|988E|01FF5765,30,0000003D,04,00,04FE|01A1|0000|0000,1,,010055,B6\r\n
    // &&W153,866496072214805,000,0,,250514053315,A,14.615378,121.036901,7,1.4,0,332,0,204,515|3|4AC3|03347480,31,0000003D,02,00,0538|01A3|0000|0000,1,,0100F9|0200F8,3E
    static parseGpsMessage(message) {

        const messageAscii = message.toString();
        const components = messageAscii.split(',').slice(0, 25); // get the first 25 values

        const sequenceNumber = components[0].slice(2, 3);
        const imei = components[1];
        let alarmCode = components[3];
        alarmCode = alarmNames[alarmCode] || alarmCode;
        const alarmData = components[4];
        const ibutton = alarmCode === 'Swiping Card RFID Event' ? alarmData : '';

        const timestamp = components[5];
        const year = "20" + timestamp.slice(0, 2);
        const month = timestamp.slice(2, 4);
        const day = timestamp.slice(4, 6);
        const hour = timestamp.slice(6, 8);
        const minute = timestamp.slice(8, 10);
        const second = timestamp.slice(10, 12);
        const gpsTimestamp = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

        const latitude = components[7];
        const longitude = components[8];
        const satelliteCount = components[9];
        const speed = components[11];
        const course = components[12];
        const mccMnc = components[15].split('|').slice(0, 2).join('0');
        const gsm = components[16];

        const inStatus = parseInt(components[18], 16);
        const sosCode = (inStatus & 0b00000001) > 0 ? 1 : 0;
        const accCode = (inStatus & 0b00000010) > 0 ? 1 : 0;
        const doorCode = (inStatus & 0b00000100) > 0 ? 1 : 0;
        const outStatus = components[19];

        const hexVoltages = components[20];
        const hexValues = hexVoltages.split('|');
        const extBat = parseInt(hexValues[0], 16) / 100;
        const intBat = parseInt(hexValues[1], 16) / 100;

        // Modify temp - handle multiple temperature sensors
        let temperature;
        if (components[23]) {
            // Split by '|' to handle multiple temperature sensors
            const tempSensors = components[23].split('|');
            const tempValues = [];

            // Process each temperature sensor
            for (const sensor of tempSensors) {
                const index = sensor.substring(0, 2); // Sensor index
                const sign = sensor.substring(2, 3) === '8' ? -1 : 1; // Check if negative
                const temp = parseInt(sensor.substring(3, 6), 16) / 10 * sign; // Convert hex to decimal and apply sign
                tempValues.push(`${temp.toFixed(1)}`); // Store as "index:value" format
            }

            // Join all temperature values with semicolons
            temperature = tempValues.join(' | ');
        }

        const data = `SeqNum=${sequenceNumber}; gpsTimestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; course=${course}; mcc/mnc=${mccMnc}; gsm=${gsm}; inStatus=${inStatus}; sosCode=${sosCode}; accCode=${accCode}; doorCode=${doorCode}; outStatus=${outStatus}; extBat=${extBat}; intBat=${intBat}; temp=${temperature}; ibutton=${ibutton}; alarmCode=${alarmCode}`;

        return {
            imei,
            data,
            sequenceNumber,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            mccMnc,
            gsm,
            inStatus,
            sosCode,
            accCode,
            doorCode,
            outStatus,
            extBat,
            intBat,
            temperature,
            alarmCode,
            ibutton
        };
    }

    // Function to construct command message
    // $$O20,864714068055525,100F4
    static constructCommandMessage = (command, deviceId, sequenceNumber) => {

        const commandLength = 1 + 15 + 1 + command.length;

        // Construct the message without CRC
        const messageWithoutCrc = `${COMMAND_HEADER}${sequenceNumber}${commandLength},${deviceId},${command}`;

        // Sum of Bytes % 256
        const checksum = Istartek2goHandler.checksum(messageWithoutCrc);
        const message = `${messageWithoutCrc}${checksum}\r\n`;

        return message;
    }

    // Function to parse command response
    // 26264f32332c3836343731343036383035353532352c3130302c4f4b43310d0a
    // &&O23,864714068055525,100,OKC1
    static parseCommandResponse = (message) => {
        const data = message.toString().slice(6, -4);
        const responseToCommand = data;

        return {
            responseToCommand
        };
    }

    // Checksum is the sum of Bytes % 256 calculation function
    static checksum = (data) => {
        let checksum = 0;
        for (let i = 0; i < data.length; i++) {
            checksum += data.charCodeAt(i);
        }
        let result = (checksum % 256).toString(16).toUpperCase();
        if (result.length < 2) {
            result = '0' + result;
        }
        return result;
    }

    // Function to calculate cyclically from 0x3A to 0x7E
    static incrementSequenceNumber = (value) => {
        const max = 0x7E; // 126
        const min = 0x3A; // 58
        let seq = ':';

        // Check if value is defined and not null
        value ? seq = value : null;
        let result = seq.charCodeAt(0); // Get the numeric value of the character
        if (result === max) {
            result = min;
        } else {
            result += 1; // Increment the value by 1
            if (result > max) {
                result = min + (result - max - 1); // Wrap around if it exceeds max
            }
        }
        return String.fromCharCode(result); // Convert back to character
    }

}
module.exports = Istartek2goHandler;
