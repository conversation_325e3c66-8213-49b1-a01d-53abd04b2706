// npm install --save easy-crc
// npm i async-mutex

const net = require('net');
const { getDbInstance, createTableForIMEI, imeiLastRecords } = require('./db.js');
const db = getDbInstance();
const { crc16 } = require('easy-crc');
const { Mutex } = require('async-mutex');
const dbWriteLock = new Mutex();

const destinations = [
    { name: 'TracksolidPro', host: 'gpsdev-hk.tracksolidpro.com', port: 21100 },
    //{ name: 'TracksolidPro', host: 'gpsdev.tracksolid.com', port: 21100 },
    { name: 'Fleetnav-Concox_Wetrack', host: '*************', port: 5058 },
    { name: 'Fleetnav-Concox_VL103', host: '*************', port: 5927 },
    //{ name: 'iNav-Office', host: '**************', port: 6300 },
];

const lastCommandSentTime = new Map(); // Track when commands were last sent to each IMEI
const imeiErrorCount = new Map(); // Track error counts per IMEI

let destinationConnectionId = 1;
let logCount = 10;
const IDLE_TIMEOUT = 60 * 1000;
const SQL_BATCH_INTERVAL = 10 * 1000;
const COMMAND_COOLDOWN = 120 * 1000; // 2 minutes in milliseconds
const MAX_ERRORS_PER_IMEI = 10; // Maximum number of errors to log per IMEI
let commandSequenceNumber = 0;
const imeiLatLngTimeAcc = new Map();

const options = {
    timeZone: 'Asia/Manila',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
};

// Constants
const IMEI_START_BYTE_GV25 = '3525';
const END_BIT = '0d0a';
const START_BIT = '7878';
const LOGIN_MESSAGE_TYPE = 0x01;
const HEARTBEAT_MESSAGE_TYPE = 0x13;
const COMMAND_MESSAGE_TYPE = 0x80;

// GV25 message types
const GPS_MESSAGE_TYPE_12 = 0x12;
const ALARM_MESSAGE_TYPE_16 = 0x16;
const COMMAND_RESPONSE_TYPE_15 = 0x15;

// VL103 message types
const START_BIT_EXTENDED = '7979';
const DATA_MESSAGE_TYPE_94 = 0x94;
const GPS_MESSAGE_TYPE_22 = 0x22;
const GPS_MESSAGE_TYPE_A0 = 0xA0;
const ALARM_MESSAGE_TYPE_A4 = 0xA4;
const ALARM_MESSAGE_TYPE_GEOFENCE_26 = 0x26;
const COMMAND_RESPONSE_TYPE_21 = 0x21;
const TIME_MESSAGE_TYPE_8A = 0x8a; //7878058a0001fc960d0a

// 7878252618080102040CCF00E0E4400D527EB012544409020303C7F80065914C0603030207A291250D0A

// Message types that need response
const MESSAGE_NEED_RESPONSE_TYPE = [
    LOGIN_MESSAGE_TYPE,
    HEARTBEAT_MESSAGE_TYPE,
    ALARM_MESSAGE_TYPE_16,
    ALARM_MESSAGE_TYPE_A4
];

class JimiHandler {
    constructor() {

        this.destinationSockets = new Map();
        this.sqlBatch = [];
        this.sqlBatchTimer = null;

        this.isGV25 = false;
        this.imei = '';
        this.data = '';

        this.initializeSQLBatch();
    }

    initializeSQLBatch() {
        this.sqlBatchTimer = setInterval(this.sqlLogBatch.bind(this), SQL_BATCH_INTERVAL);
    }

    async sqlLogBatch() {
        if (this.sqlBatch.length > 0 && this.imei) {
            await dbWriteLock.runExclusive(async () => {
                return new Promise((resolve, reject) => {
                    createTableForIMEI(db, this.imei);

                    const sanitizedImei = this.imei.replace(/[^a-zA-Z0-9_]/g, '');
                    const query = `INSERT INTO gps_data_${sanitizedImei} (
                        serverTimestamp,
                        gpsTimestamp,
                        latitude,
                        longitude,
                        speed,
                        course,
                        satelliteCount,
                        accCode
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

                    db.serialize(() => {
                        db.run("BEGIN TRANSACTION");
                        const stmt = db.prepare(query);

                        try {
                            for (const message of this.sqlBatch) {
                                if (message.messageType !== LOGIN_MESSAGE_TYPE) {
                                    stmt.run(message.data, (err) => {
                                            if (err) {
                                                console.error(`Error inserting data for IMEI ${this.imei}:`, err.message);
                                            }
                                        }
                                    );
                                }
                            }

                            stmt.finalize((err) => {
                                if (err) {
                                    console.error('Error finalizing statement:', err.message);
                                    db.run("ROLLBACK");
                                    reject(err);
                                } else {
                                    db.run("COMMIT", (err) => {
                                        if (err) {
                                            console.error('Error committing transaction:', err.message);
                                            db.run("ROLLBACK");
                                            reject(err);
                                        } else {
                                            this.sqlBatch = [];
                                            resolve();
                                        }
                                    });
                                }
                            });
                        } catch (err) {
                            console.error('Error in batch processing:', err);
                            stmt.finalize();
                            db.run("ROLLBACK");
                            reject(err);
                        }
                    });
                });
            }).catch((err) => {
                console.error('Database write failed:', err);
            });
        }
    }

    connectToDestinationSockets(isGV25) {
        for (const destination of destinations) {
            const name = destination.name;
            if (name === 'Fleetnav-Concox_Wetrack' && isGV25 ||
                name === 'Fleetnav-Concox_VL103' && !isGV25 ||
                name !== 'Fleetnav-Concox_Wetrack' && name !== 'Fleetnav-Concox_VL103') {

                const socket = new net.Socket();

                socket.connect(destination.port, destination.host, () => {
                    socket.destination = destination;
                    socket.connectionId = destinationConnectionId++;
                    this.destinationSockets.set(socket.connectionId, socket);
                    //console.log(`${this.imei} connected to ${destination.name}[${socket.connectionId}]`);
                });

                socket.on('error', err => {
                    if (logCount !== 0) {
                        console.log(`Error connecting to ${destination.name}: ${err.message}`);
                        logCount--;
                    }
                    socket.destroy();
                });

                socket.on('close', () => {
                    socket.removeAllListeners();
                    this.destinationSockets.delete(socket.connectionId);
                })

                socket.setTimeout(IDLE_TIMEOUT, () => {
                    socket.destroy();
                });
            }
        }
    }

    async handle(deviceSocket, data, commandAndResponse) {
        const serverTimestamp = new Date().toISOString();

        try {
            const parsedMessage = JimiHandler.parseMessage(data);

            if (parsedMessage.imei) {
                this.imei = parsedMessage.imei;
                this.isGV25 = parsedMessage.imei.slice(0, 4) === IMEI_START_BYTE_GV25;
            }
            if (parsedMessage.data) this.data = `messageType=${parsedMessage.messageType}; ${parsedMessage.data}`;

            // If no this.imei, destroy the socket
            if (!this.imei) {
                deviceSocket.destroy();
                return;
            }

            // Store last records
            const existingData = imeiLastRecords.get(this.imei) || {};
            const updatedData = {
                imei: this.imei,
                serverTimestamp: serverTimestamp,
                gpsTimestamp: parsedMessage.gpsTimestamp || existingData.gpsTimestamp,
                latitude: parsedMessage.latitude || existingData.latitude,
                longitude: parsedMessage.longitude || existingData.longitude,
                speed: parsedMessage.speed || existingData.speed,
                course: parsedMessage.course || existingData.course,
                satelliteCount: parsedMessage.satelliteCount || existingData.satelliteCount,
                accCode: parsedMessage.accCode || existingData.accCode
            };

            imeiLastRecords.set(this.imei, updatedData);

            // Filter out GPS messages
            const latLngTimeAcc = imeiLatLngTimeAcc.get(this.imei) || [0, 0, 0, 0];
            const dist = Math.sqrt(Math.pow(latLngTimeAcc[0] - parsedMessage.latitude, 2) + Math.pow(latLngTimeAcc[1] - parsedMessage.longitude, 2)) * 111 * 1000;
            const timeDiff = (new Date(parsedMessage.gpsTimestamp) - new Date(latLngTimeAcc[2])) / 1000;
            const isAccChanged = (parsedMessage.accCode !== latLngTimeAcc[3]);
            const isLatLng = (parsedMessage.latitude !== 0 && parsedMessage.longitude !== 0);

            // Insert message into SQLite database
            if (
                (parsedMessage.gpsTimestamp &&
                    isLatLng &&
                    (dist > 50 || timeDiff > 3600 / 6 || isAccChanged)
                ) || !imeiLatLngTimeAcc.has(this.imei)
            ) {
                if (parsedMessage.messageType === GPS_MESSAGE_TYPE_12) {
                    this.sqlBatch.push({
                        messageType: parsedMessage.messageType,
                        data: [
                            serverTimestamp,
                            parsedMessage.gpsTimestamp,
                            parsedMessage.latitude,
                            parsedMessage.longitude,
                            parsedMessage.speed,
                            parsedMessage.course,
                            parsedMessage.satelliteCount,
                            ''
                        ]
                    });
                } else if (parsedMessage.messageType === GPS_MESSAGE_TYPE_22 || parsedMessage.messageType === GPS_MESSAGE_TYPE_A0) {
                    this.sqlBatch.push({
                        messageType: parsedMessage.messageType,
                        data: [
                            serverTimestamp,
                            parsedMessage.gpsTimestamp,
                            parsedMessage.latitude,
                            parsedMessage.longitude,
                            parsedMessage.speed,
                            parsedMessage.course,
                            parsedMessage.satelliteCount,
                            parsedMessage.accCode
                        ]
                    });
                }
                imeiLatLngTimeAcc.set(
                    this.imei,
                    [parseFloat(parsedMessage.latitude),
                    parseFloat(parsedMessage.longitude),
                    parsedMessage.gpsTimestamp,
                    parsedMessage.accCode]
                );
            }

            // Check if destination sockets already exist for this IMEI
            if (this.destinationSockets && this.destinationSockets.size === 0) {
                await new Promise((resolve) => {
                    this.connectToDestinationSockets(this.isGV25);

                    // Check if all destination sockets are connected
                    const intervalId = setInterval(() => {
                        if (this.destinationSockets.size === destinations.length) {
                            clearInterval(intervalId);
                            clearTimeout(timeoutId);
                            // Only resolve the promise here, after sockets have connected
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to reject the promise if the condition isn't met in time
                    const timeoutId = setTimeout(() => {
                        clearInterval(intervalId);
                        resolve();
                    }, 5 * 1000)
                });
            }

            // Forward device data and destination response
            if (this.destinationSockets && this.destinationSockets.size > 0) {
                this.destinationSockets.forEach(socket => {

                    // Send device data to destination
                    if (socket && socket.writable) {
                        //console.log(`Send ${this.imei}[${deviceSocket.connectionId}] data to ${socket.destination.name}[${socket.connectionId}] ${data.toString('hex')}`);
                        socket.write(data, err => {
                            if (err) {
                                socket.destroy();
                                this.destinationSockets.delete(socket.connectionId);
                            }
                        });
                    } else {
                        this.destinationSockets.delete(socket.connectionId);
                        return; // Skip to the next socket
                    }

                    // Send destination response to device
                    socket.removeAllListeners('data');
                    socket.on('data', (response) => {
                        if (deviceSocket && deviceSocket.writable) {
                            //console.log(`${this.imei} received response from ${socket.destination.name}[${socket.connectionId}]`);
                            deviceSocket.write(response, err => {
                                if (err) {
                                    deviceSocket.destroy();
                                    //console.log(`Error sending response to ${this.imei} [${deviceSocket.connectionId}]`);
                                }
                            });
                        } else {
                            deviceSocket.destroy();
                            //console.log(`Error ${this.imei} not writable [${deviceSocket.connectionId}]`);
                        }
                    });
                });
            }

            // Send response to device
            if (this.isMessageNeedResponse(parsedMessage)) {
                const responseMessage = this.createResponse(parsedMessage.sequenceNumber, parsedMessage.messageType);
                //console.log(`Response to ${this.imei} [${deviceSocket.connectionId}]:`, responseMessage);
                if (deviceSocket && deviceSocket.writable) {
                    deviceSocket.write(responseMessage, (err) => {
                        if (err) {
                            deviceSocket.destroy();
                            console.log(`Error sending my response to ${this.imei}`);
                        }
                    });
                }
                // Add delay before sending command
                await new Promise(resolve => setTimeout(resolve, 100)); // add delay
            }

            // Send command to device
            if (commandAndResponse && commandAndResponse.has(this.imei)) {
                const commandData = commandAndResponse.get(this.imei);
                if (!commandData.response && parsedMessage.messageType !== LOGIN_MESSAGE_TYPE) {
                    const currentTime = Date.now();
                    const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                    // Check if the cooldown period has passed since the last command was sent
                    if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                    const fullCommand = commandData.command.endsWith('#') ? commandData.command : `${commandData.command}#`;
                    const commandMessage = JimiHandler.constructCommandMessage(fullCommand, commandSequenceNumber++);
                    console.log(`Send command to ${this.imei} [${deviceSocket.connectionId}]:`, fullCommand);

                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending command to ${this.imei}`);
                                }
                            });

                            // Update the last sent time for this IMEI
                            lastCommandSentTime.set(this.imei, currentTime);
                        }
                    } else {
                        console.log(`Command to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN / 1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                    }
                }
            }

            // Store command response
            if (parsedMessage.responseToCommand) {
                const commandData = commandAndResponse.get(this.imei);
                if (commandData) {
                    commandData.response = parsedMessage.responseToCommand;
                    commandAndResponse.set(this.imei, commandData);
                }
            }

        } catch (error) {
            // Get current error count for this IMEI or initialize to 0
            const errorCount = imeiErrorCount.get(this.imei) || 0;

            // Only log if we haven't reached the maximum number of errors for this IMEI
            if (errorCount < MAX_ERRORS_PER_IMEI) {
                console.error(`Jimi Handle Error [${this.imei}]:`, error.message);
                // Increment the error count for this IMEI
                imeiErrorCount.set(this.imei, errorCount + 1);
            }
        }
    }

    cleanup() {
        if (this.sqlBatchTimer) {
            clearInterval(this.sqlBatchTimer);
        }

        if (this.sqlBatch.length > 0) {
            return this.sqlLogBatch()
                .catch(err => console.error('Error during final batch save:', err))
                .finally(() => {
                    this.destinationSockets.forEach(socket => {
                        socket.destroy();
                    });
                    this.destinationSockets.clear();
                });
        } else {
            this.destinationSockets.forEach(socket => {
                socket.destroy();
            });
            this.destinationSockets.clear();
            return Promise.resolve();
        }
    }

    // HELPER FUNCTIONS
    static getMessageSegment = (message, start, end) => message.slice(start, end).toString('hex');
    static getAsciiSegment = (message, start, end) => message.slice(start, end).toString('ascii');
    isMessageNeedResponse = (parsedMessage) => MESSAGE_NEED_RESPONSE_TYPE.includes(parsedMessage.messageType);


    // Parse messages
    static parseMessage = (message) => {
        const startBit = JimiHandler.getMessageSegment(message, 0, 2);

        if (startBit === START_BIT) {
            const packetLength = message[2];
            const messageType = message[3];

            switch (messageType) {
                case LOGIN_MESSAGE_TYPE:
                    return {
                        ...JimiHandler.parseLoginMessage(message),
                        messageType
                    };
                case HEARTBEAT_MESSAGE_TYPE:
                    return {
                        ...JimiHandler.parseHeartbeatMessage(message),
                        messageType
                    };
                case GPS_MESSAGE_TYPE_12:
                    return {
                        ...JimiHandler.parseGpsMessage12(message),
                        messageType
                    };
                case GPS_MESSAGE_TYPE_22:
                    return {
                        ...JimiHandler.parseGpsMessage22(message),
                        messageType
                    };
                case GPS_MESSAGE_TYPE_A0:
                    return {
                        ...JimiHandler.parseGpsMessageA0(message),
                        messageType
                    };
                case ALARM_MESSAGE_TYPE_16:
                    return {
                        ...JimiHandler.parseAlarmMessage16(message),
                        messageType
                    };
                case ALARM_MESSAGE_TYPE_A4:
                    return {
                        ...JimiHandler.parseAlarmMessage16(message),
                        messageType
                    };
                case COMMAND_RESPONSE_TYPE_15:
                    return {
                        ...JimiHandler.parseCommandResponse(message, packetLength),
                        messageType
                    };
            }
        } else if (startBit === START_BIT_EXTENDED) {
            const packetLengthExt = parseInt(JimiHandler.getMessageSegment(message, 2, 4), 16);
            const messageType = message[4];

            switch (messageType) {
                case DATA_MESSAGE_TYPE_94:
                    return {
                        ...JimiHandler.parseDataMessage(message, packetLengthExt),
                        messageType
                    };
                case COMMAND_RESPONSE_TYPE_21:
                    return {
                        ...JimiHandler.parseCommandResponseExt(message, packetLengthExt),
                        messageType
                    };
            }
        } else {
            throw new Error(`Jimi Unparsed ${message.toString('hex')}`);
        }
    };

    // Function to parse login message
    // Login message: 787811010869066061549888807132010bbb389f0d0a
    static parseLoginMessage = (message) => {
        const imei = JimiHandler.getMessageSegment(message, 4, 12).replace(/^0+/, '');
        const hardwareModelCode = JimiHandler.getMessageSegment(message, 12, 14);
        const timezoneLanguage = JimiHandler.getMessageSegment(message, 14, 16);
        const sequenceNumber = JimiHandler.getMessageSegment(message, 16, 18);
        const crc16Value = JimiHandler.getMessageSegment(message, 18, 20);
        const endBit = JimiHandler.getMessageSegment(message, 20, 22);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `imei=${imei}; hardwareCode=${hardwareModelCode}; timezone=${timezoneLanguage}; sequenceNumber=${sequenceNumber}`;
        return {
            imei,
            data,
            sequenceNumber
        };
    };
    // Function to create a server response message
    // Response: 787805010bbb27a50d0a
    createResponse = (sequenceNumber, messageType) => {
        const startBit = Buffer.from('7878', 'hex');
        const packetLength = Buffer.from('05', 'hex');
        const messageTypeHex = Buffer.from([messageType]);
        const seqNum = Buffer.from(sequenceNumber, 'hex');
        const messageWithoutCrc = Buffer.concat([packetLength, messageTypeHex, seqNum]);

        // Calculate CRC-16/X-25
        const crc16Value = crc16('X-25', messageWithoutCrc).toString(16);
        const crc16Buffer = Buffer.from(crc16Value, 'hex');
        const endBit = Buffer.from('0d0a', 'hex');

        return Buffer.concat([startBit, messageWithoutCrc, crc16Buffer, endBit]);
    };

    // Function to parse hearbeat message
    // Heartbeat message: 78780a1346060400021039a2350d0a
    // Resonse to hearbeat: 78 78 05 13 10 39 C1 AB 0D 0A 78 78 05 13 10 39 C1 AB 0D 0A (from Tracksolid)
    static parseHeartbeatMessage = (message) => {
        const statusCode = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16);
        // parse statusCode
        const isCutOffFuel = (statusCode & 0b10000000) > 0;
        const isPositionFixed = (statusCode & 0b01000000) > 0;
        const alarmCode = (statusCode >>> 3) & 0b111;
        let alarm;
        switch (alarmCode) {
            case 0b100: alarm = 'SOS'; break;
            case 0b011: alarm = 'Low battery alert'; break;
            case 0b010: alarm = 'Power cutoff'; break;
            case 0b001: alarm = 'Vibrating alert'; break;
            case 0b000: alarm = 'Normal'; break;
            default: alarm = 'Unknown alarm code';
        }
        const isPowerConnected = (statusCode & 0b00000100) > 0;
        const accCode = (statusCode & 0b00000010) > 0;
        const isDefenseOn = (statusCode & 0b00000001) > 0;

        const voltageCode = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        // parse voltageCode
        let voltage;
        switch (voltageCode) {
            case 0x00: voltage = 'No power - power off'; break;
            case 0x01: voltage = 'Battery extremely low - no sending SMS'; break;
            case 0x02: voltage = 'Battery very low - alert triggered'; break;
            case 0x03: voltage = 'Battery 30%'; break;
            case 0x04: voltage = 'Battery 60%'; break;
            case 0x05: voltage = 'Battery 80%'; break;
            case 0x06: voltage = 'Battery 100%'; break;
            default: voltage = 'Unknown voltage code';
        }

        const gsmCode = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        // parse gsmCode
        let gsmSignal;
        switch (gsmCode) {
            case 0x00: gsmSignal = 'No signal'; break;
            case 0x01: gsmSignal = 'Extremely weak signal'; break;
            case 0x02: gsmSignal = 'Weak signal'; break;
            case 0x03: gsmSignal = 'Good signal'; break;
            case 0x04: gsmSignal = 'Strong signal'; break;
            default: gsmSignal = 'Unknown GSM code';
        }

        const languageCode = JimiHandler.getMessageSegment(message, 7, 9);
        const sequenceNumber = JimiHandler.getMessageSegment(message, 9, 11);
        const crc16Value = JimiHandler.getMessageSegment(message, 11, 13);
        const endBit = JimiHandler.getMessageSegment(message, 13, 15);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `isCutOffFuel=${isCutOffFuel}; isPositionFixed=${isPositionFixed}; alarm=${alarm}; isPowerConnected=${isPowerConnected}; accCode=${accCode}; isDefenseOn=${isDefenseOn}; voltage=${voltage}; gsmSignal=${gsmSignal}`;

        return {
            data,
            languageCode,
            sequenceNumber
        };
    };


    // Function to parse data message
    // Data message: 7979015c9404414c4d313d43343b414c4d323d43343b414c4d333d34443b535441313d34303b4459443d30313b534f533d2c2c3b43454e5445523d3b4746454e4345312c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b4746454e4345322c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b4746454e4345332c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b4746454e4345342c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b4746454e4345352c4f46462c302c302e3030303030302c302e3030303030302c3330302c494e206f72204f55542c313b494d53493d3531353033393231363231313637313b49434349443d38393633303332333233303033303138333731303b0bbfd0320d0a
    static parseDataMessage = (message, packetLength) => {
        const contentTypeCode = JimiHandler.getMessageSegment(message, 5, 6);
        const contentCode = JimiHandler.getAsciiSegment(message, 6, packetLength + 4 - 4)
            .replace(/;/g, '; ')
        //.replace(/,/g, '');
        const sequenceNumber = JimiHandler.getMessageSegment(message, packetLength + 4 - 4, packetLength + 4 - 2);
        const crc16Value = JimiHandler.getMessageSegment(message, packetLength + 4 - 2, packetLength + 4);
        const endBit = JimiHandler.getMessageSegment(message, packetLength + 4, packetLength + 4 + 2);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `contentTypeCode=${contentTypeCode}; ${contentCode}`;

        return {
            data,
            sequenceNumber
        };
    };

    // Function to parse GPS message
    static parseGpsMessage12 = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 22, 24), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 24, 25), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;

        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 25, 27), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 27, 30), 16);

        const sequenceNumber = JimiHandler.getMessageSegment(message, 30, 32);
        const crc16Value = JimiHandler.getMessageSegment(message, 32, 34);
        const endBit = JimiHandler.getMessageSegment(message, 34, 36);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            sequenceNumber
        };
    }

    // GPS message: 7878222218071f02152ec301916e920cfc661000c53a0203032be5004ee2010300001ea5d40d0a
    // GPS message: 7878262218071f0d2e34c8011c04380d4c96600fd40b020303a11100357701020001d178ee00e730a50d0a
    static parseGpsMessage22 = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;
        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 22, 24), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 24, 25), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;

        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 25, 27), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 27, 30), 16);
        const accCode = parseInt(JimiHandler.getMessageSegment(message, 30, 31), 16);

        const gpsIntervalCode = parseInt(JimiHandler.getMessageSegment(message, 31, 32), 16);
        let gpsInterval;
        switch (gpsIntervalCode) {
            case 0x00: gpsInterval = 'Upload fixed inteval'; break;
            case 0x01: gpsInterval = 'Upload fixed distance'; break;
            case 0x02: gpsInterval = 'Upload at cornering point'; break;
            case 0x03: gpsInterval = 'Upload upon ACC status change'; break;
            case 0x04: gpsInterval = 'Upload last fixed from moving to stopped'; break;
            case 0x05: gpsInterval = 'Upload last fixed from network interruption'; break;
            case 0x06: gpsInterval = 'Force upload fix upon ephemeris refresh'; break;
            case 0x07: gpsInterval = 'Upload a fix upon key press'; break;
            case 0x08: gpsInterval = 'Upload location upon power-on'; break;
            case 0x09: gpsInterval = 'Not used'; break;
            case 0x0A: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0B: gpsInterval = 'Parse the longitude and latitude packet over WiFi'; break;
            case 0x0C: gpsInterval = 'Upload upon LJDW command'; break;
            case 0x0D: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0E: gpsInterval = 'GPSDUP upload (upload at a fixed interval in still state)'; break;
            default: gpsInterval = 'Unknown upload type';
        }

        const gpsRealtimeCode = parseInt(JimiHandler.getMessageSegment(message, 32, 33), 16);
        let gpsRealtime;
        switch (gpsRealtimeCode) {
            case 0x00: gpsRealtime = 'Real-time upload'; break;
            case 0x01: gpsRealtime = 'Re-upload'; break;
            default: gpsRealtime = 'Unknown real-time upload';
        }

        const isMileageData = JimiHandler.getMessageSegment(message, 2, 3) === '26';
        const mileageIndex = isMileageData ? 33 : 0;
        const sequenceNumberIndex = isMileageData ? 37 : 33;
        const crc16ValueIndex = isMileageData ? 39 : 35;
        const endBitIndex = isMileageData ? 41 : 37;

        const mileage = isMileageData ? parseInt(JimiHandler.getMessageSegment(message, mileageIndex, mileageIndex + 4), 16) : '';
        const sequenceNumber = JimiHandler.getMessageSegment(message, sequenceNumberIndex, sequenceNumberIndex + 2);
        const crc16Value = JimiHandler.getMessageSegment(message, crc16ValueIndex, crc16ValueIndex + 2);
        const endBit = JimiHandler.getMessageSegment(message, endBitIndex, endBitIndex + 2);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}; acc=${accCode}; interval=${gpsInterval}; realtime=${gpsRealtime}; mileage=${mileage}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            sequenceNumber
        };
    }

    // GPS message old VL103: 787829a018071d060d1ecf01916ce20cfc621000140002030300004ac3000000000334747d0108000004d3090d0a
    // GPS message new VL149: 78782DA019041A073826CF0191854E0CFC8BF00F14A802030300004AA6000000000334D27D010200000025CA005462390D0A
    static parseGpsMessageA0Old = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 22, 24), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 24, 25), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;

        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 25, 29), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 29, 37), 16);
        const accCode = parseInt(JimiHandler.getMessageSegment(message, 37, 38), 16);

        const gpsIntervalCode = parseInt(JimiHandler.getMessageSegment(message, 38, 39), 16);
        let gpsInterval;
        switch (gpsIntervalCode) {
            case 0x00: gpsInterval = 'Upload fixed inteval'; break;
            case 0x01: gpsInterval = 'Upload fixed distance'; break;
            case 0x02: gpsInterval = 'Upload at cornering point'; break;
            case 0x03: gpsInterval = 'Upload upon ACC status change'; break;
            case 0x04: gpsInterval = 'Upload last fixed from moving to stopped'; break;
            case 0x05: gpsInterval = 'Upload last fixed from network interruption'; break;
            case 0x06: gpsInterval = 'Force upload fix upon ephemeris refresh'; break;
            case 0x07: gpsInterval = 'Upload a fix upon key press'; break;
            case 0x08: gpsInterval = 'Upload location upon power-on'; break;
            case 0x09: gpsInterval = 'Not used'; break;
            case 0x0A: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0B: gpsInterval = 'Parse the longitude and latitude packet over WiFi'; break;
            case 0x0C: gpsInterval = 'Upload upon LJDW command'; break;
            case 0x0D: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0E: gpsInterval = 'GPSDUP upload (upload at a fixed interval in still state)'; break;
            default: gpsInterval = 'Unknown upload type';
        }

        const gpsRealtimeCode = parseInt(JimiHandler.getMessageSegment(message, 39, 40), 16);
        let gpsRealtime;
        switch (gpsRealtimeCode) {
            case 0x00: gpsRealtime = 'Real-time upload'; break;
            case 0x01: gpsRealtime = 'Re-upload'; break;
            default: gpsRealtime = 'Unknown real-time upload';
        }

        const sequenceNumber = JimiHandler.getMessageSegment(message, 40, 42);
        const crc16Value = JimiHandler.getMessageSegment(message, 42, 44);
        const endBit = JimiHandler.getMessageSegment(message, 44, 46);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}; acc=${accCode}; interval=${gpsInterval}; realtime=${gpsRealtime}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            sequenceNumber
        };
    }

    // GPS message new VL149: 78782DA019041A073826CF0191854E0CFC8BF00F14A802030300004AA6000000000334D27D010200000025CA005462390D0A
    static parseGpsMessageA0 = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 22, 24), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 24, 25), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;

        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 25, 29), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 29, 37), 16);
        const accCode = parseInt(JimiHandler.getMessageSegment(message, 37, 38), 16);

        const gpsIntervalCode = parseInt(JimiHandler.getMessageSegment(message, 38, 39), 16);
        let gpsInterval;
        switch (gpsIntervalCode) {
            case 0x00: gpsInterval = 'Upload fixed inteval'; break;
            case 0x01: gpsInterval = 'Upload fixed distance'; break;
            case 0x02: gpsInterval = 'Upload at cornering point'; break;
            case 0x03: gpsInterval = 'Upload upon ACC status change'; break;
            case 0x04: gpsInterval = 'Upload last fixed from moving to stopped'; break;
            case 0x05: gpsInterval = 'Upload last fixed from network interruption'; break;
            case 0x06: gpsInterval = 'Force upload fix upon ephemeris refresh'; break;
            case 0x07: gpsInterval = 'Upload a fix upon key press'; break;
            case 0x08: gpsInterval = 'Upload location upon power-on'; break;
            case 0x09: gpsInterval = 'Not used'; break;
            case 0x0A: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0B: gpsInterval = 'Parse the longitude and latitude packet over WiFi'; break;
            case 0x0C: gpsInterval = 'Upload upon LJDW command'; break;
            case 0x0D: gpsInterval = 'Upload the fix after the device goes still'; break;
            case 0x0E: gpsInterval = 'GPSDUP upload (upload at a fixed interval in still state)'; break;
            default: gpsInterval = 'Unknown upload type';
        }

        const gpsRealtimeCode = parseInt(JimiHandler.getMessageSegment(message, 39, 40), 16);
        let gpsRealtime;
        switch (gpsRealtimeCode) {
            case 0x00: gpsRealtime = 'Real-time upload'; break;
            case 0x01: gpsRealtime = 'Re-upload'; break;
            default: gpsRealtime = 'Unknown real-time upload';
        }

        /*
        const mileage = parseInt(JimiHandler.getMessageSegment(message, 40, 44), 16);

        const sequenceNumber = JimiHandler.getMessageSegment(message, 44, 46);
        const crc16Value = JimiHandler.getMessageSegment(message, 46, 48);
        const endBit = JimiHandler.getMessageSegment(message, 48, 50);

        if (endBit !== END_BIT) {
            //throw new Error('Invalid end bit');
        }*/

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}; acc=${accCode}; interval=${gpsInterval}; realtime=${gpsRealtime}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            //mileage,
            //sequenceNumber
        };
    }

    // Function to parse alarm message
    static parseAlarmMessage1 = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccMncLength = JimiHandler.getMessageSegment(message, 22, 23);
        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 23, 25), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 25, 26), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;
        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 26, 28), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 28, 31), 16);

        const statusCode = parseInt(JimiHandler.getMessageSegment(message, 31, 32), 16);
        const isCutOffFuel = (statusCode & 0b10000000) > 0;
        const isPositionFixed = (statusCode & 0b01000000) > 0;
        const alarmCode = (statusCode >>> 3) & 0b111;
        let alarm;
        switch (alarmCode) {
            case 0b100: alarm = 'SOS'; break;
            case 0b011: alarm = 'Low battery alert'; break;
            case 0b010: alarm = 'Power cutoff'; break;
            case 0b001: alarm = 'Vibrating alert'; break;
            case 0b000: alarm = 'Normal'; break;
            default: alarm = 'Unknown alarm code';
        }
        const isPowerConnected = (statusCode & 0b00000100) > 0;
        const accCode = (statusCode & 0b00000010) > 0;
        const isDefenseOn = (statusCode & 0b00000001) > 0;

        const voltageCode = parseInt(JimiHandler.getMessageSegment(message, 32, 33), 16);
        // parse voltageCode
        let voltage;
        switch (voltageCode) {
            case 0x00: voltage = 'No power - power off'; break;
            case 0x01: voltage = 'Battery extremely low - no sending SMS'; break;
            case 0x02: voltage = 'Battery very low - alert triggered'; break;
            case 0x03: voltage = 'Battery 30%'; break;
            case 0x04: voltage = 'Battery 60%'; break;
            case 0x05: voltage = 'Battery 80%'; break;
            case 0x06: voltage = 'Battery 100%'; break;
            default: voltage = 'Unknown voltage code';
        }

        const gsmCode = parseInt(JimiHandler.getMessageSegment(message, 33, 34), 16);
        // parse gsmCode
        let gsmSignal;
        switch (gsmCode) {
            case 0x00: gsmSignal = 'No signal'; break;
            case 0x01: gsmSignal = 'Extremely weak signal'; break;
            case 0x02: gsmSignal = 'Weak signal'; break;
            case 0x03: gsmSignal = 'Good signal'; break;
            case 0x04: gsmSignal = 'Strong signal'; break;
            default: gsmSignal = 'Unknown GSM code';
        }

        const alertCode = parseInt(JimiHandler.getMessageSegment(message, 34, 35), 16);
        // parse alertCode
        let alert;
        switch (alertCode) {
            case 0x00: alert = 'No alert'; break;
            case 0x01: alert = 'SOS alert'; break;
            case 0x02: alert = 'Power Cut alert'; break;
            case 0x03: alert = 'Vibration alert'; break;
            case 0x04: alert = 'Enter fence alert'; break;
            case 0x05: alert = 'Exit fence alert'; break;
            case 0x06: alert = 'Overspeed alert'; break;
            case 0x09: alert = 'Towing alert'; break;
            case 0x0a: alert = 'GPS lost alert'; break;
            case 0x0b: alert = 'GPS recovered alert'; break;
            case 0x0c: alert = 'Power on alert'; break;
            case 0x0d: alert = 'GPS first fix alert'; break;
            case 0x0e: alert = 'Low external battery alert'; break;
            case 0x0f: alert = 'Power disconnected - ext battery protection alert'; break;
            case 0x10: alert = 'SIM changed alert'; break;
            case 0x11: alert = 'Power off alert'; break;
            case 0x12: alert = 'Airplane mode - ext battery protection alert'; break;
            case 0x13: alert = 'Tampering alert'; break;
            case 0x14: alert = 'Door alert'; break;
            case 0x15: alert = 'Power off - low battery alert'; break;
            case 0x16: alert = 'Sound control alert'; break;
            case 0x17: alert = 'Rogue base station detected alert'; break;
            case 0x18: alert = 'Cover removed alert'; break;
            case 0x19: alert = 'Low int battery alert'; break;
            case 0x20: alert = 'Entered deep sleep mode alert'; break;
            case 0x23: alert = 'Fall alert'; break;
            case 0x29: alert = 'Harsh acceleration alert'; break;
            case 0x2a: alert = 'Sharp left alert'; break;
            case 0x2b: alert = 'Sharp right alert'; break;
            case 0x2c: alert = 'Collision alert'; break;
            case 0x30: alert = 'Harsh braking alert'; break;
            case 0x32: alert = 'Device unplugged alert'; break;
            case 0x53: alert = 'Fuel theft alert'; break;
            case 0x71: alert = 'Refuel alert'; break;
            case 0x77: alert = 'ADC1 high voltage alert'; break;
            case 0x78: alert = 'ADC1 low voltage alert'; break;
            case 0xc5: alert = 'Engine on alert'; break;
            case 0xc6: alert = 'Engine off alert'; break;
            case 0xc7: alert = 'Fatigue driving alert'; break;
            case 0xc8: alert = 'Fatigue driving repeated alert'; break;
            case 0xc9: alert = 'Long idle alert'; break;
            case 0xe6: alert = 'IN1 active alert'; break;
            case 0xe7: alert = 'IN1 inactive alert'; break;
            case 0xe8: alert = 'IN2 active alert'; break;
            case 0xe9: alert = 'IN2 inactive alert'; break;
            case 0xfe: alert = 'Ignition on alert'; break;
            case 0xff: alert = 'Ignition off alert'; break;
            default: alert = 'Unknown language code';
        }

        const lanuageCode = JimiHandler.getMessageSegment(message, 35, 36);

        const sequenceNumber = JimiHandler.getMessageSegment(message, 36, 38);
        const crc16Value = JimiHandler.getMessageSegment(message, 38, 40);
        const endBit = JimiHandler.getMessageSegment(message, 40, 42);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}; isCuttOffFuel=${isCutOffFuel}; isPositionFixed=${isPositionFixed}; alarm=${alarm}; isPowerConnected=${isPowerConnected}; accCode=${accCode}; isDefenseOn=${isDefenseOn}; voltage=${voltage}; gsmSignal=${gsmSignal}; alert=${alert}; lanuageCode=${lanuageCode}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            sequenceNumber
        }
    }

    // Alarm message: 78782da418071d091222cf01916d000cfc63100004001002030300004ac30000000003347480050604c800ff020fae410d0a
    static parseAlarmMessage16 = (message) => {
        const year = parseInt(JimiHandler.getMessageSegment(message, 4, 5), 16) + 2000;
        const month = parseInt(JimiHandler.getMessageSegment(message, 5, 6), 16);
        const day = parseInt(JimiHandler.getMessageSegment(message, 6, 7), 16);
        const hour = parseInt(JimiHandler.getMessageSegment(message, 7, 8), 16);
        const minute = parseInt(JimiHandler.getMessageSegment(message, 8, 9), 16);
        const second = parseInt(JimiHandler.getMessageSegment(message, 9, 10), 16);
        const gpsTimestamp = `${year.toString().padStart(4, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;

        const satelliteCountHex = JimiHandler.getMessageSegment(message, 10, 11);
        const satelliteCount = parseInt(satelliteCountHex.slice(1, 2), 16);

        const latitudeValue = parseInt(JimiHandler.getMessageSegment(message, 11, 15), 16);
        const latitude = (latitudeValue / 1800000).toFixed(6);
        const longitudeValue = parseInt(JimiHandler.getMessageSegment(message, 15, 19), 16);
        const longitude = (longitudeValue / 1800000).toFixed(6);
        const speed = parseInt(JimiHandler.getMessageSegment(message, 19, 20), 16);

        const courseStatusCode = parseInt(JimiHandler.getMessageSegment(message, 20, 22), 16);
        const gpsRealTime = (courseStatusCode & 0b0010000000000000) >>> 13;
        const positioned = (courseStatusCode & 0b0001000000000000) >>> 12;
        const gpsPositioning = `realtime=${gpsRealTime}; position=${positioned}`;
        const course = courseStatusCode & 0b1111111111;

        const mccMncLength = JimiHandler.getMessageSegment(message, 22, 23);
        const mccCode = parseInt(JimiHandler.getMessageSegment(message, 23, 25), 16);
        const mncCode = parseInt(JimiHandler.getMessageSegment(message, 25, 26), 16).toString().padStart(2, '0');
        const mccMnc = `${mccCode}${mncCode}`;
        const lacCode = parseInt(JimiHandler.getMessageSegment(message, 26, 30), 16);
        const cellIdCode = parseInt(JimiHandler.getMessageSegment(message, 30, 38), 16);

        const statusCode = parseInt(JimiHandler.getMessageSegment(message, 38, 39), 16);
        const isCutOffFuel = (statusCode & 0b10000000) > 0;
        const isPositionFixed = (statusCode & 0b01000000) > 0;
        const alarmCode = (statusCode >>> 3) & 0b111;
        let alarm;
        switch (alarmCode) {
            case 0b100: alarm = 'SOS'; break;
            case 0b011: alarm = 'Low battery alert'; break;
            case 0b010: alarm = 'Power cutoff'; break;
            case 0b001: alarm = 'Vibrating alert'; break;
            case 0b000: alarm = 'Normal'; break;
            default: alarm = 'Unknown alarm code';
        }
        const isPowerConnected = (statusCode & 0b00000100) > 0;
        const accCode = (statusCode & 0b00000010) > 0;
        const isDefenseOn = (statusCode & 0b00000001) > 0;

        const voltageCode = parseInt(JimiHandler.getMessageSegment(message, 39, 40), 16);
        // parse voltageCode
        let voltage;
        switch (voltageCode) {
            case 0x00: voltage = 'No power - power off'; break;
            case 0x01: voltage = 'Battery extremely low - no sending SMS'; break;
            case 0x02: voltage = 'Battery very low - alert triggered'; break;
            case 0x03: voltage = 'Battery 30%'; break;
            case 0x04: voltage = 'Battery 60%'; break;
            case 0x05: voltage = 'Battery 80%'; break;
            case 0x06: voltage = 'Battery 100%'; break;
            default: voltage = 'Unknown voltage code';
        }

        const gsmCode = parseInt(JimiHandler.getMessageSegment(message, 40, 41), 16);
        // parse gsmCode
        let gsmSignal;
        switch (gsmCode) {
            case 0x00: gsmSignal = 'No signal'; break;
            case 0x01: gsmSignal = 'Extremely weak signal'; break;
            case 0x02: gsmSignal = 'Weak signal'; break;
            case 0x03: gsmSignal = 'Good signal'; break;
            case 0x04: gsmSignal = 'Strong signal'; break;
            default: gsmSignal = 'Unknown GSM code';
        }

        const alertCode = parseInt(JimiHandler.getMessageSegment(message, 41, 42), 16);
        // parse alertCode
        let alert;
        switch (alertCode) {
            case 0x00: alert = 'No alert'; break;
            case 0x01: alert = 'SOS alert'; break;
            case 0x02: alert = 'Power Cut alert'; break;
            case 0x03: alert = 'Vibration alert'; break;
            case 0x04: alert = 'Enter fence alert'; break;
            case 0x05: alert = 'Exit fence alert'; break;
            case 0x06: alert = 'Overspeed alert'; break;
            case 0x09: alert = 'Towing alert'; break;
            case 0x0a: alert = 'GPS lost alert'; break;
            case 0x0b: alert = 'GPS recovered alert'; break;
            case 0x0c: alert = 'Power on alert'; break;
            case 0x0d: alert = 'GPS first fix alert'; break;
            case 0x0e: alert = 'Low external battery alert'; break;
            case 0x0f: alert = 'Power disconnected - ext battery protection alert'; break;
            case 0x10: alert = 'SIM changed alert'; break;
            case 0x11: alert = 'Power off alert'; break;
            case 0x12: alert = 'Airplane mode - ext battery protection alert'; break;
            case 0x13: alert = 'Tampering alert'; break;
            case 0x14: alert = 'Door alert'; break;
            case 0x15: alert = 'Power off - low battery alert'; break;
            case 0x16: alert = 'Sound control alert'; break;
            case 0x17: alert = 'Rogue base station detected alert'; break;
            case 0x18: alert = 'Cover removed alert'; break;
            case 0x19: alert = 'Low int battery alert'; break;
            case 0x20: alert = 'Entered deep sleep mode alert'; break;
            case 0x23: alert = 'Fall alert'; break;
            case 0x29: alert = 'Harsh acceleration alert'; break;
            case 0x2a: alert = 'Sharp left alert'; break;
            case 0x2b: alert = 'Sharp right alert'; break;
            case 0x2c: alert = 'Collision alert'; break;
            case 0x30: alert = 'Harsh braking alert'; break;
            case 0x32: alert = 'Device unplugged alert'; break;
            case 0x53: alert = 'Fuel theft alert'; break;
            case 0x71: alert = 'Refuel alert'; break;
            case 0x77: alert = 'ADC1 high voltage alert'; break;
            case 0x78: alert = 'ADC1 low voltage alert'; break;
            case 0xc5: alert = 'Engine on alert'; break;
            case 0xc6: alert = 'Engine off alert'; break;
            case 0xc7: alert = 'Fatigue driving alert'; break;
            case 0xc8: alert = 'Fatigue driving repeated alert'; break;
            case 0xc9: alert = 'Long idle alert'; break;
            case 0xe6: alert = 'IN1 active alert'; break;
            case 0xe7: alert = 'IN1 inactive alert'; break;
            case 0xe8: alert = 'IN2 active alert'; break;
            case 0xe9: alert = 'IN2 inactive alert'; break;
            case 0xfe: alert = 'Ignition on alert'; break;
            case 0xff: alert = 'Ignition off alert'; break;
            default: alert = 'Unknown language code';
        }

        const lanuageCode = JimiHandler.getMessageSegment(message, 42, 43);
        const fenceNoCode = JimiHandler.getMessageSegment(message, 43, 44);

        const sequenceNumber = JimiHandler.getMessageSegment(message, 44, 46);
        const crc16Value = JimiHandler.getMessageSegment(message, 46, 48);
        const endBit = JimiHandler.getMessageSegment(message, 48, 50);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const data = `timestamp=${gpsTimestamp}; satCount=${satelliteCount}; lat/lon=${latitude} ${longitude}; speed=${speed}; ${gpsPositioning}; course=${course}; mccMnc=${mccMnc}; lac=${lacCode}; cellId=${cellIdCode}; isCuttOffFuel=${isCutOffFuel}; isPositionFixed=${isPositionFixed}; alarm=${alarm}; isPowerConnected=${isPowerConnected}; accCode=${accCode}; isDefenseOn=${isDefenseOn}; voltage=${voltage}; gsmSignal=${gsmSignal}; alert=${alert}; lanuageCode=${lanuageCode}; fenceNoCode=${fenceNoCode}`;

        return {
            data,
            gpsTimestamp,
            satelliteCount,
            latitude,
            longitude,
            speed,
            course,
            accCode,
            sequenceNumber
        }
    }


    // Function to construct a Command message
    // Command mssage: 787813800B000000005354415455532300022A205B5C0D0A
    // Command mssage: 787837802F0005702E5345525645522C312C6770736465762D686B2E747261636B736F6C696470726F2E636F6D2C32313130302300027F8A75B60D0A
    static constructCommandMessage = (command, sequenceNumber) => {
        const startBit = Buffer.from(START_BIT, 'hex');
        const serverCode = Buffer.from('00000000', 'hex');
        const commandBuffer = Buffer.from(command, 'ascii');
        const messageLength = commandBuffer.length;
        const payloadLength = serverCode.length + messageLength;
        const packetLength = 1 + 1 + payloadLength + 2 + 2 + 2; // Message type + length + payload length + language code + sequence no + crc16
        const messageType = Buffer.from([COMMAND_MESSAGE_TYPE]);
        const languageCode = Buffer.from('0002', 'hex');
        const seqNum = Buffer.from(sequenceNumber.toString(16).padStart(4, '0'), 'hex'); // Ensure 4 bytes for sequence number
        const messageWithoutCrc = Buffer.concat([Buffer.from([packetLength]), messageType, Buffer.from([payloadLength]), serverCode, commandBuffer, languageCode, seqNum]);

        // Calculate CRC-16/X-25
        const crc16Value = crc16('X-25', messageWithoutCrc).toString(16);
        const crc16Buffer = Buffer.from(crc16Value, 'hex');
        const endBit = Buffer.from(END_BIT, 'hex');

        return Buffer.concat([startBit, messageWithoutCrc, crc16Buffer, endBit]);
    };


    // Function to parse command response message
    static parseCommandResponse = (message, packetLength) => {
        const serverCode = JimiHandler.getMessageSegment(message, 5, 9);
        const statusCode = JimiHandler.getMessageSegment(message, 9, 10);
        const data = JimiHandler.getAsciiSegment(message, 10, packetLength).replace(/;/g, '; ');;
        const sequenceNumber = JimiHandler.getMessageSegment(message, packetLength, packetLength + 2);
        const crc16Value = JimiHandler.getMessageSegment(message, packetLength + 2, packetLength + 4);
        const endBit = JimiHandler.getMessageSegment(message, packetLength + 4, packetLength + 6);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const responseToCommand = data;

        return {
            statusCode,
            responseToCommand,
            sequenceNumber
        };
    };

    // Command response messag: 797900B0210000000001426174746572793A332E3934562C4E4F524D414C3B20534F433A4C696E6B2055703B4C544652425F534F433A4C696E6B20446F776E3B4C5445205369676E616C204C6576656C3A5374726F6E673B204750533A536561726368696E6720736174656C6C6974653B20535653205573656420696E206669783A30283332293B20475053205369676E616C204C6576656C3A204143433A4F4E3B20446566656E73653A4F46463B200005E4BD0D0A
    static parseCommandResponseExt = (message, packetLength) => {
        const serverCode = JimiHandler.getMessageSegment(message, 5, 9);
        const statusCode = JimiHandler.getMessageSegment(message, 9, 10);
        const data = JimiHandler.getAsciiSegment(message, 10, packetLength).replace(/;/g, '; ');;
        const sequenceNumber = JimiHandler.getMessageSegment(message, packetLength, packetLength + 2);
        const crc16Value = JimiHandler.getMessageSegment(message, packetLength + 2, packetLength + 4);
        const endBit = JimiHandler.getMessageSegment(message, packetLength + 4, packetLength + 6);

        if (endBit !== END_BIT) {
            throw new Error('Invalid end bit');
        }

        const responseToCommand = data;

        return {
            statusCode,
            data,
            responseToCommand,
            sequenceNumber
        };
    };

}
module.exports = JimiHandler;
