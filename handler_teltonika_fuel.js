// npm install fs
// npm install crc

const net = require('net');
const { getDbInstance, createTableForIMEI, imeiLastRecords } = require('./db_fuel.js');
const db = getDbInstance();
const fs = require('fs');
const crc = require('crc'); // CRC16-IBM
const { Mutex } = require('async-mutex');  // Add this at the top
const dbWriteLock = new Mutex();


const destinations = [
    { name: 'Loconav-Teltonika', host: '***********', port: 5035 },
    { name: 'Fleetx', host: '************', port: 4444 },
];

const lastCommandSentTime = new Map(); // Track when commands were last sent to each IMEI
const imeiErrorCount = new Map(); // Track error counts per IMEI

let destinationConnectionId = 1;
let logCount = 10;
const IDLE_TIMEOUT = 30 * 1000;
const SQL_BATCH_INTERVAL = 10 * 1000;
const COMMAND_COOLDOWN = 120 * 1000; // 2 minutes in milliseconds
const MAX_ERRORS_PER_IMEI = 10; // Maximum number of errors to log per IMEI
const imeiLatLngTimeAcc = new Map();
const imeiFuelLevel = new Map();
const imeiLastFuelLevel = new Map();

const options = {
    timeZone: 'Asia/Manila',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
};

// Constants
const START_BIT_LOGIN = '000f';
const START_BIT_DATA = '00000000';
const CODEC_8E_MESSAGE_TYPE = 0x8E;
const CODEC_8_MESSAGE_TYPE = 0x08;
const CODEC_12_MESSAGE_TYPE = 0x0C;
const CODEC_13_MESSAGE_TYPE = 0x0D;
const COMMAND_MESSAGE = 0x05;
const COMMAND_RESPONSE = 0x06;

// Message types that need response
const MESSAGE_NEED_RESPONSE_TYPE = [
    START_BIT_LOGIN, CODEC_8_MESSAGE_TYPE, CODEC_8E_MESSAGE_TYPE
];

// Load the IO ID to name mapping from a text file
const ioNames = {};
const ioMappingData = fs.readFileSync('io_names.csv', 'utf8');
ioMappingData.split('\n').forEach(line => {
    const [ioId, ioName] = line.trim().split(',');
    ioNames[ioId] = ioName;
});


class TeltonikaHandler {
    constructor() {

        this.destinationSockets = new Map();
        this.sqlBatch = [];
        this.sqlBatchTimer = null;
        this.imei = '';
        this.data = '';

        this.initializeSQLBatch();
    }

    initializeSQLBatch() {
        this.sqlBatchTimer = setInterval(this.sqlLogBatch.bind(this), SQL_BATCH_INTERVAL);
    }

    async sqlLogBatch() {
        if (this.sqlBatch.length > 0 && this.imei) {
            await dbWriteLock.runExclusive(async () => {  // Lock the function
                createTableForIMEI(db, this.imei);  // Ensure table for the IMEI is created

                const sanitizedImei = this.imei.replace(/[^a-zA-Z0-9_]/g, '');
                const query = `INSERT INTO gps_data_${sanitizedImei} (serverTimestamp, gpsTimestamp, latitude, longitude, speed, course, satelliteCount, accCode, extBat, intBat, temperature, fuelLevel, analogIn) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

                db.serialize(() => {
                    const stmt = db.prepare(query);

                    for (const message of this.sqlBatch) {
                        if (message.messageType !== START_BIT_LOGIN) {
                            stmt.run(message.data, (err) => {
                                if (err) {
                                    console.error('Error inserting message', err.message);
                                }
                            });
                        }
                    }

                    stmt.finalize((err) => {
                        if (err) {
                            console.error('Error finalizing statement', err.message);
                        }
                    });
                });

                // Clear the batch after insertion
                this.sqlBatch = [];
            }).catch((err) => {
                console.error('Database write failed:', err);
            });
        }
    }

    connectToDestinationSockets() {
        for (const destination of destinations) {
            const socket = new net.Socket();

            socket.connect(destination.port, destination.host, () => {
                socket.destination = destination;
                socket.connectionId = destinationConnectionId++;
                this.destinationSockets.set(socket.connectionId, socket);
                //console.log(`${this.imei} connected to ${destination.name}[${socket.connectionId}]`);
            });

            socket.on('error', err => {
                if (logCount !== 0) {
                    console.log(`Error connecting to ${destination.name}: ${err.message}`);
                    logCount--;
                }
                socket.destroy();
            });

            socket.on('close', () => {
                socket.removeAllListeners();
                this.destinationSockets.delete(socket.connectionId);
            })

            socket.setTimeout(IDLE_TIMEOUT, () => {
                socket.destroy();
            });
        }
    }

    async handle(deviceSocket, data, commandAndResponse) {

        const serverTimestamp = new Date().toISOString();

        try {
            const parsedMessage = TeltonikaHandler.parseMessage(data);
            //if (this.imei === '860896052075447') console.log(data.toString('hex'));

            if (parsedMessage.imei) this.imei = parsedMessage.imei;
            if (parsedMessage.data) this.data = JSON.stringify(parsedMessage.data);

            // Store fuel level
            if (parsedMessage.fuelLevel) {
                imeiFuelLevel.set(this.imei, [parsedMessage.fuelLevel, parsedMessage.rawTimestamp]);
                //console.log(imeiFuelLevel.get(this.imei));
            }

            // Add fuel level to data
            let modifiedData = data; // Default to original data
            if (parsedMessage.messageType === CODEC_8E_MESSAGE_TYPE && imeiFuelLevel.has(this.imei)) {
                modifiedData = TeltonikaHandler.addFuelLevelToData(data, imeiFuelLevel.get(this.imei));
                //console.log('Modified data:', modifiedData.toString('hex'));
            }

            // If no this.imei, destroy the socket
            if (!this.imei) {
                deviceSocket.destroy();
                return;
            }

            // Insert LOGIN message into SQLite database
            if (parsedMessage.messageType === START_BIT_LOGIN) {
                this.sqlBatch.push({
                    messageType: START_BIT_LOGIN,
                    data: [serverTimestamp]
                });
            }

            // Insert message into SQLite database
            if ((parsedMessage.messageType === CODEC_8_MESSAGE_TYPE || parsedMessage.messageType === CODEC_8E_MESSAGE_TYPE)) {
                parsedMessage.data.forEach(dataPoint => {

                    // Filter out GPS messages
                    const latLngTimeAcc = imeiLatLngTimeAcc.get(this.imei) || [0, 0, 0, 0];
                    const dist = Math.sqrt(Math.pow(latLngTimeAcc[0] - dataPoint.latitude, 2) + Math.pow(latLngTimeAcc[1] - dataPoint.longitude, 2)) * 111 * 1000;
                    const timeDiff = (new Date(dataPoint.gpsTimestamp) - new Date(latLngTimeAcc[2])) / 1000;
                    const isAccChanged = (dataPoint.io.Ignition !== latLngTimeAcc[3]);
                    const isLatLng = (dataPoint.latitude !== 0 && dataPoint.longitude !== 0);

                    // Convert unsigned 8-bit integer to signed 8-bit integer
                    const temperature = (dataPoint.io.LLS_1_Temperature & 0xFF) >= 0x80
                        ? dataPoint.io.LLS_1_Temperature - 0x100
                        : dataPoint.io.LLS_1_Temperature;


                    const FIVE_MINUTES_MS = 5 * 60 * 1000;
                    const MAX_FUEL_LEVEL = 65532;
                    const MAX_OMNICOM_FUEL_LEVEL = 66532;

                    let fuelLevelData = null;
                    const gpsTimestamp = new Date(dataPoint.gpsTimestamp);

                    if (imeiFuelLevel.has(this.imei)) {
                        // For RS232 fuel rods
                        const [lastFuelLevel, lastFuelLevelTime] = imeiFuelLevel.get(this.imei);
                        const fuelTimeDiff = Math.abs(gpsTimestamp - new Date(lastFuelLevelTime));
                        fuelLevelData = fuelTimeDiff < FIVE_MINUTES_MS ? lastFuelLevel : null;

                    } else if (dataPoint.io.LLS_1_Fuel_Level < MAX_FUEL_LEVEL) {
                        // For integrated fuel sensor
                        fuelLevelData = parseInt(dataPoint.io.LLS_1_Fuel_Level) / 10;

                    } else if (parseInt(dataPoint.io.LLS_4_Fuel_Level) < MAX_OMNICOM_FUEL_LEVEL) {
                        // For Omnicom fuel rod
                        fuelLevelData = dataPoint.io.LLS_4_Fuel_Level;
                    }

                    const isFuelLevel = fuelLevelData !== null;

                    const extBat = dataPoint.io.External_Voltage / 1000;
                    const intBat = dataPoint.io.Battery_Voltage / 1000;

                    // Store last records
                    const existingData = imeiLastRecords.get(this.imei) || {};
                    const updatedData = {
                        imei: this.imei,
                        serverTimestamp: serverTimestamp,
                        gpsTimestamp: parsedMessage.messageType === START_BIT_LOGIN ? existingData.gpsTimestamp : dataPoint.gpsTimestamp,
                        latitude: parsedMessage.messageType === START_BIT_LOGIN ? existingData.latitude : dataPoint.latitude,
                        longitude: parsedMessage.messageType === START_BIT_LOGIN ? existingData.longitude : dataPoint.longitude,
                        speed: parsedMessage.messageType === START_BIT_LOGIN ? existingData.speed : dataPoint.speed,
                        course: parsedMessage.messageType === START_BIT_LOGIN ? existingData.course : dataPoint.course,
                        satelliteCount: parsedMessage.messageType === START_BIT_LOGIN ? existingData.satelliteCount : dataPoint.satelliteCount,
                        accCode: parsedMessage.messageType === START_BIT_LOGIN ? existingData.accCode : dataPoint.io.Ignition,
                        extBat: parsedMessage.messageType === START_BIT_LOGIN ? existingData.extBat : dataPoint.extBat,
                        intBat: parsedMessage.messageType === START_BIT_LOGIN ? existingData.intBat : dataPoint.intBat,
                        temperature: parsedMessage.messageType === START_BIT_LOGIN ? existingData.temperature : temperature,
                        fuelLevel: parsedMessage.messageType === START_BIT_LOGIN ? existingData.fuelLevel : fuelLevelData,
                        analogIn: parsedMessage.messageType === START_BIT_LOGIN ? existingData.analogIn : dataPoint.io.Analog_Input_1
                    };

                    imeiLastRecords.set(this.imei, updatedData);

                    if (
                        (dataPoint.gpsTimestamp &&
                            isLatLng &&
                            (dist > 50 || timeDiff > 3600 / 6 || isAccChanged || (isFuelLevel && timeDiff > 3600 / 6))
                        ) || !imeiLatLngTimeAcc.has(this.imei)
                    ) {
                        if (dataPoint.eventIoId != 0) {
                            return
                        }
                        this.sqlBatch.push({
                            messageType: dataPoint.messageType,
                            data: [
                                serverTimestamp,
                                dataPoint.gpsTimestamp,
                                dataPoint.latitude,
                                dataPoint.longitude,
                                dataPoint.speed,
                                dataPoint.course,
                                dataPoint.satelliteCount,
                                dataPoint.io.Ignition,
                                extBat,
                                intBat,
                                temperature,
                                fuelLevelData,
                                dataPoint.io.Analog_Input_1
                            ]
                        });
                        imeiLatLngTimeAcc.set(
                            this.imei,
                            [parseFloat(dataPoint.latitude),
                            parseFloat(dataPoint.longitude),
                            dataPoint.gpsTimestamp,
                            dataPoint.io.Ignition]
                        );
                    }
                });
            }

            // Check if destination sockets already exist for this IMEI
            if (this.destinationSockets && this.destinationSockets.size === 0) {
                await new Promise((resolve) => {
                    this.connectToDestinationSockets();

                    // Check if all destination sockets are connected
                    const intervalId = setInterval(() => {
                        if (this.destinationSockets.size === destinations.length) {
                            clearInterval(intervalId);
                            clearTimeout(timeoutId);
                            // Only resolve the promise here, after sockets have connected
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to reject the promise if the condition isn't met in time
                    const timeoutId = setTimeout(() => {
                        clearInterval(intervalId);
                        resolve();
                    }, 5 * 1000)
                });
            }

            // Forward device data and destination response
            if (this.destinationSockets && this.destinationSockets.size > 0) {
                this.destinationSockets.forEach(socket => {

                    // Send device data to destination
                    if (socket && socket.writable) {
                        //console.log(`Send ${this.imei}[${deviceSocket.connectionId}] data to ${socket.destination.name}[${socket.connectionId}] ${data.toString('hex')}`);
                        socket.write(modifiedData, err => {
                            if (err) {
                                socket.destroy();
                                this.destinationSockets.delete(socket.connectionId);
                            }
                        });
                    } else {
                        this.destinationSockets.delete(socket.connectionId);
                        return; // Skip to the next socket
                    }

                    // Send destination response to device
                    /*socket.removeAllListeners('data');
                    socket.on('data', (response) => {
                        if (deviceSocket && deviceSocket.writable) {
                            if (this.imei === '352592573459310') console.log(`${this.imei} received response from ${socket.destination.name}[${socket.connectionId}]`);
                            deviceSocket.write(response, err => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending response to ${this.imei} [${deviceSocket.connectionId}]`);
                                }
                            });
                        } else {
                            deviceSocket.destroy();
                            console.log(`Error ${this.imei} not writable [${deviceSocket.connectionId}]`);
                        }
                    });*/
                });
            }

            // Send response to device
            if (this.isMessageNeedResponse(parsedMessage)) {
                const responseMessage = parsedMessage.response;
                // console.log(`Response to ${this.imei} [${deviceSocket.connectionId}]:`, responseMessage);
                if (deviceSocket && deviceSocket.writable) {
                    deviceSocket.write(responseMessage, (err) => {
                        if (err) {
                            deviceSocket.destroy();
                            console.log(`Error sending my response to ${this.imei}`);
                        }
                    });
                }
                // Add delay before sending command
                await new Promise(resolve => setTimeout(resolve, 100)); // add delay
            }

            // Send command to device
            if (commandAndResponse && commandAndResponse.has(this.imei)) {
                const commandData = commandAndResponse.get(this.imei);
                if (!commandData.response && parsedMessage.messageType !== START_BIT_LOGIN) {
                    const currentTime = Date.now();
                    const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                    // Check if the cooldown period has passed since the last command was sent
                    if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                        const commandMessage = TeltonikaHandler.constructCommandMessage(commandData.command);
                        console.log(`Send command to ${this.imei} [${deviceSocket.connectionId}]:`, commandData.command);

                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending command to ${this.imei}`);
                                }
                            });

                            // Update the last sent time for this IMEI
                            lastCommandSentTime.set(this.imei, currentTime);
                        }
                    } else {
                        console.log(`Command to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN/1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                    }
                }
            }

            // Store command response
            if (parsedMessage.responseToCommand) {
                const commandData = commandAndResponse.get(this.imei);
                if (commandData) {
                    commandData.response = parsedMessage.responseToCommand;
                    commandAndResponse.set(this.imei, commandData);
                    console.log(`Response to Command ${this.imei} [${deviceSocket.connectionId}]:`, commandData.response);
                }
            }

        } catch (error) {
            console.error(`Teltonika Handle Error [${this.imei}]:`, error.message);
        }
    }

    cleanupDestinationSockets() {
        if (this.destinationSockets) {
            this.destinationSockets.forEach(socket => {
                socket.removeAllListeners('data');
            });
        }
        if (this.sqlBatchTimer) {
            if (this.sqlBatch.length > 0) {
                this.sqlLogBatch()
                    .then(() => {
                        // Clear the timer only after data has been inserted
                        clearInterval(this.sqlBatchTimer);
                        this.sqlBatchTimer = null;
                    });
            } else {
                clearInterval(this.sqlBatchTimer);
                this.sqlBatchTimer = null;
            }
        }
    }

    cleanup() {
        this.cleanupDestinationSockets();
    }


    // HELPER FUNCTIONS
    static pad = (number) => (number < 10 ? '0' : '') + number;
    static getMessageSegment = (message, start, end) => message.slice(start, end).toString('hex');
    static getAsciiSegment = (message, start, end) => message.slice(start, end).toString('ascii');
    isMessageNeedResponse = (parsedMessage) => MESSAGE_NEED_RESPONSE_TYPE.includes(parsedMessage.messageType);


    // Parse messages
    static parseMessage = (message) => {
        const startBitLogin = TeltonikaHandler.getMessageSegment(message, 0, 2);
        const startBitData = TeltonikaHandler.getMessageSegment(message, 0, 4);

        if (startBitLogin === START_BIT_LOGIN) {
            const messageType = startBitLogin;

            return {
                ...TeltonikaHandler.parseLoginMessage(message),
                messageType
            }

        } else if (startBitData === START_BIT_DATA) {
            const messageType = message[8];

            switch (messageType) {
                case CODEC_8_MESSAGE_TYPE:
                    //console.log('CODEC_8_MESSAGE_TYPE');
                    return {
                        ...TeltonikaHandler.parseCodec8Message(message),
                        messageType
                    };
                case CODEC_8E_MESSAGE_TYPE:
                    //console.log('CODEC_8E_MESSAGE_TYPE');
                    return {
                        ...TeltonikaHandler.parseCodec8ExtendedMessage(message),
                        messageType
                    };
                case CODEC_12_MESSAGE_TYPE:
                    return {
                        ...TeltonikaHandler.parseCommandResponse(message),
                        messageType
                    };
                case CODEC_13_MESSAGE_TYPE:
                    return {
                        ...TeltonikaHandler.parseCodec13Message(message),
                        messageType
                    };

            }
        } else {
            // Get current error count for this IMEI or initialize to 0
            const errorCount = imeiErrorCount.get(this.imei) || 0;

            // Only log if we haven't reached the maximum number of errors for this IMEI
            if (errorCount < MAX_ERRORS_PER_IMEI) {
                console.error(`Teltonika Handle Error [${this.imei}]:`, error.message);
                // Increment the error count for this IMEI
                imeiErrorCount.set(this.imei, errorCount + 1);
            }
        }
    };

    // Data	000F383636393037303538383034333531
    // IMEI	866907058804351
    static parseLoginMessage = (message) => {
        const packetLength = message[1]; // Length 000F
        const imei = message.slice(2, packetLength + 2).toString();
        const data = imei;

        return {
            imei,
            data,
            response: Buffer.from('01', 'hex')
        };
    }


    // Codec 8 message
    // 000000000000004f0801000001916fc729a80047bd62cd098ed2a70010012d0d0014001006ef01f0011503c8004501010108b5000fb60007423261180014cd3016ce524c43103f44001302f10000c92f10012f6c5d00010000b445
    static parseCodec8Message = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData = buffer.readUInt8(9);

        //console.log('dataFieldLength', dataFieldLength, 'codecId', codecId, 'numberOfData', numberOfData);

        const avlData = [];
        let bufferIO = [];
        let avlBuffer = buffer.slice(10);

        for (let i = 0; i < numberOfData; i++) {
            // Format the gpsTimestamp from 2023-03-20T23:31:09.000Z to 'YYYY-MM-DD HH:mm:ss'
            const unixTimestamp = Number(avlBuffer.readBigInt64BE());
            const rawTimestamp = new Date(unixTimestamp);
            const gpsTimestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
            const priority = avlBuffer.readUInt8(8);
            avlBuffer = avlBuffer.slice(9);
            //console.log('gpsTimestamp', gpsTimestamp, 'priority', priority);

            const longitude = avlBuffer.readInt32BE() / 10000000;
            const latitude = avlBuffer.readInt32BE(4) / 10000000;
            const altitude = avlBuffer.readInt16BE(8);
            const course = avlBuffer.readUInt16BE(10);
            const satelliteCount = avlBuffer.readUInt8(12);
            const speed = avlBuffer.readUInt16BE(13);
            avlBuffer = avlBuffer.slice(15);
            //console.log('longitude', longitude, 'latitude', latitude, 'altitude', altitude, 'course', course, 'satelliteCount', satelliteCount, 'speed', speed);

            const eventIoId = avlBuffer.readUInt8();
            const totalIO = avlBuffer.readUInt8(1);
            avlBuffer = avlBuffer.slice(2);
            //console.log('eventIoId', eventIoId, 'totalIO', totalIO);

            bufferIO = [];
            const countOneByteIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);
            //console.log('countOneByteIO', countOneByteIO);

            for (let j = 0; j < countOneByteIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt8(1);
                avlBuffer = avlBuffer.slice(2);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('OneByteIO', countOneByteIO);

            const countTwoBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countTwoBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt16BE(1);
                avlBuffer = avlBuffer.slice(3);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('TwoBytesIO', countTwoBytesIO);

            const countFourBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countFourBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt32BE(1);
                avlBuffer = avlBuffer.slice(5);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('FourBytesIO', countFourBytesIO);

            const countEightBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countEightBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.slice(1, 9).toString('hex');
                avlBuffer = avlBuffer.slice(9);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('EightBytesIO', countEightBytesIO);

            const io = Object.assign({}, ...bufferIO);
            avlData.push({ codecId, numberOfData, gpsTimestamp, priority, longitude, latitude, altitude, course, satelliteCount, speed, eventIoId, totalIO, io });

            if (avlBuffer.length < (dataFieldLength - 3) / numberOfData) break;
        }

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');

        const data = avlData

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            data, //avlData,
            //numberOfData2,
            //crc16
            response: Buffer.from((numberOfData + '').padStart(8, '0'), 'hex')
        };
    }

    // Codec 8 extended message
    // 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
    // LLS 1 fuel
    // 00000000000000978e0100000192666d63d0004824ccb208b625e7ffd400fd0a000000000018000600ef0100f00100150500c800004501007100000c00b5000c00b60008004237da0018000000ce4ac30043000000440000000d270f001100520012ffe50013fc3800c904eb000400f10000c92f00100000001f000c0000d283027c0334747d0002000b00000002163d04a4000e00000002189f9f030000010000a6cd
    // id: 201, value: fuelLevel - ioTwoBytes
    // Data Count = 9
    // 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
    // 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
    static parseCodec8ExtendedMessage = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData = buffer.readUInt8(9);
        //console.log(`dataFieldLength: ${dataFieldLength}, codecId: ${codecId}, numberOfData1: ${numberOfData}`);

        const avlData = [];
        let bufferIO = [];
        let avlBuffer = buffer.slice(10);

        for (let i = 0; i < numberOfData; i++) {
            // Format the gpsTimestamp from 2023-03-20T23:31:09.000Z to 'YYYY-MM-DD HH:mm:ss'
            const unixTimestamp = Number(avlBuffer.readBigInt64BE());
            const rawTimestamp = new Date(unixTimestamp);
            const gpsTimestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
            const priority = avlBuffer.readUInt8(8);
            avlBuffer = avlBuffer.slice(9);
            //console.log(`gpsTimestamp: ${gpsTimestamp}, priority: ${priority}`);

            const longitude = avlBuffer.readInt32BE() / 10000000;
            const latitude = avlBuffer.readInt32BE(4) / 10000000;
            const altitude = avlBuffer.readInt16BE(8);
            const course = avlBuffer.readUInt16BE(10);
            const satelliteCount = avlBuffer.readUInt8(12);
            const speed = avlBuffer.readUInt16BE(13);
            avlBuffer = avlBuffer.slice(15);
            //console.log(`longitude: ${longitude}, latitude: ${latitude}, altitude: ${altitude}, course: ${course}, satelliteCount: ${satelliteCount}, speed: ${speed}`);

            const eventIoId = avlBuffer.readUInt16BE();
            const totalIO = avlBuffer.readUInt16BE(2);
            avlBuffer = avlBuffer.slice(4);
            //console.log(`eventIoId: ${eventIoId}, totalIO: ${totalIO}`);

            bufferIO = [];
            const countOneByteIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countOneByteIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt8(2);
                avlBuffer = avlBuffer.slice(3);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('OneByteIO', countOneByteIO);

            const countTwoBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countTwoBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt16BE(2);
                avlBuffer = avlBuffer.slice(4);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('TwoBytesIO', countTwoBytesIO);

            const countFourBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countFourBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt32BE(2);
                avlBuffer = avlBuffer.slice(6);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('FourBytesIO', countFourBytesIO);

            const countEightBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countEightBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.slice(2, 10).toString('hex');
                avlBuffer = avlBuffer.slice(10);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('EightBytesIO', countEightBytesIO);

            const countVariableBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countVariableBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioLength = avlBuffer.readUInt16BE(2);
                const ioValue = avlBuffer.slice(4, 4 + ioLength).toString('hex');
                avlBuffer = avlBuffer.slice(4 + ioLength);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('VariableBytesIO', countVariableBytesIO);

            const io = Object.assign({}, ...bufferIO);

            avlData.push({ codecId, numberOfData, gpsTimestamp, priority, longitude, latitude, altitude, course, satelliteCount, speed, eventIoId, totalIO, io });

            if (avlBuffer.length < (dataFieldLength - 3) / numberOfData) break;
        }

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');

        const data = avlData

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            data, //avlData,
            //numberOfData2,
            //crc16
            response: Buffer.from((numberOfData + '').padStart(8, '0'), 'hex')
        };
    }

    // Function to parse Codec 13 message
    // 00000000 0000001c 0d 01 06 00000014 66ff9032 2a52465630313037372e383442310d0a0100008822
    static parseCodec13Message = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData1 = buffer.readUInt8(9);
        //console.log(dataFieldLength, codecId, numberOfData1);

        const payloadType = buffer.readUInt8(10);
        const payloadLength = buffer.readUInt32BE(11);
        //console.log(payloadType, payloadLength);

        const unixTimestamp = buffer.readUInt32BE(15);
        const rawTimestamp = new Date(unixTimestamp * 1000);
        const timestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
        const payload = (buffer.slice(19, 19 + payloadLength)).toString().toString().replace(/\r\n.+/g, '');  // RFV01065.84AE\r\n\u0001\u0000\u0000�
        //console.log(timestamp,payload);

        // Extract fuel level data from payload CLS2 fuel sensor *RFV01077.84B1
        let fuelLevel = null;
        let fuelLevelHex = null;
        if (payload.startsWith('*RFV')) {
            fuelLevel = payload.slice(7, 12);
        }

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');
        //console.log(numberOfData2, crc16);

        const data = `${payloadType}, ${timestamp}, ${payload}, ${fuelLevel}`;

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            //payloadType,
            //payloadLength,
            rawTimestamp,
            fuelLevel,
            data,
            //numberOfData2,
            //crc16
        };
    }

    // Function to construct a Commmand message
    // Command = getinfo
    // Command message = 000000000000000F0C010500000007676574696E666F0100004312
    // 000000000000000f0c010500000007676574696e666f0100004312
    static constructCommandMessage = (command) => {

        const startBit = Buffer.alloc(4, 0x00);
        //console.log(startBit.toString('hex'));

        let commandBuffer;
        if (command.startsWith('0x')) {
            commandBuffer = Buffer.from(command.slice(2), 'hex');
        } else {
            commandBuffer = Buffer.from(command);
        }

        const packetLength = 8 + commandBuffer.length;
        const messageLength = Buffer.alloc(4);
        messageLength.writeUInt32BE(packetLength);
        //console.log(messageLength.toString('hex'));

        const messageType = Buffer.from([CODEC_12_MESSAGE_TYPE]);
        const commandCount = Buffer.from([0x01], 'hex');
        const commandType = Buffer.from([COMMAND_MESSAGE]);
        const commmandLength = Buffer.alloc(4);
        commmandLength.writeUInt32BE(commandBuffer.length);

        const messageWithoutCrc = Buffer.concat([messageType, commandCount, commandType, commmandLength, commandBuffer, commandCount]);
        //console.log(messageWithoutCrc.toString('hex'));

        const crcValue = Buffer.alloc(4);
        crcValue.writeUint32BE(crc.crc16(messageWithoutCrc));

        return Buffer.concat([startBit, messageLength, messageWithoutCrc, crcValue]);
    }

    // Function to parse command response message
    // Param ID:2004 Value:fmb.inavcloud.com+
    // message: 000000000000002d0c010600000025506172616d2049443a323030342056616c75653a666d622e696e6176636c6f75642e636f6d01000095ab
    static parseCommandResponse = (message) => {

        const commandType = TeltonikaHandler.getMessageSegment(message, 10, 11);
        const commmandLength = parseInt(TeltonikaHandler.getMessageSegment(message, 11, 15), 16);
        //console.log(commandType, commmandLength, TeltonikaHandler.getMessageSegment(message, 15, 15 + commmandLength));
        const data = TeltonikaHandler.getAsciiSegment(message, 15, 15 + commmandLength);
        //console.log(data);

        const responseToCommand = data;

        return {
            commandType,
            responseToCommand
        };
    }


    // Function to add fuel level data to the Codec 8 Exchange message
    // *RFV01055.34A8 : 000000000000001c0d01060000001467f593412a52465630313035352e333441380d0a01000
    // 00000000000000af8e010000019617473db8004824d2c008b629be0036003d0b00000000001f000c00ef0100f00100500100150500c80000450100010100b30000020000715f010701012f00000c00b5000f00b6000c004230e50018000000ce4ac300430fe10044000000090235000d270f001100f20012ff930013fc4f000500f10000c92f00c700000228001000004a22000c00084f79027c033474660002000b00000002163d04a4000e00000002189f9f030000010000676a
    // 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
    static addFuelLevelToData = (message, fuelLevelData) => {
        const buffer = Buffer.from(message, 'hex');
        const avlChunk = [];
        let countFuelDataAdded = 0;

        let avlBuffer = buffer.slice(4);
        const dataFieldLength = avlBuffer.readUInt32BE();
        const numberOfData = avlBuffer.readUInt8(5);
        const headerChunk = avlBuffer.slice(4, 6);

        avlBuffer = avlBuffer.slice(6);

        for (let i = 0; i < numberOfData; i++) {
            const startChunk = avlBuffer.slice(0, 26);
            const unixTimestamp = Number(avlBuffer.readBigInt64BE());
            const rawTimestamp = new Date(unixTimestamp);

            // Add fuel level data if the difference is less than 5 minutes
            let fuelIO = null;
            let fuelIOValue = null;
            let isFuelLevelDataAdded = 0;

            if (Math.abs(rawTimestamp - fuelLevelData[1]) < 5 * 60 * 1000) {
                isFuelLevelDataAdded = 1;
                countFuelDataAdded += 1;
                fuelIO = Buffer.from([0x00, 0xC9]);
                fuelIOValue = Buffer.alloc(2);
                fuelIOValue.writeUInt16BE(Math.floor(fuelLevelData[0] * 100), 0);
            }

            const totalIO = Buffer.alloc(2);
            totalIO.writeUInt16BE(avlBuffer.readUInt16BE(26) + isFuelLevelDataAdded, 0);

            avlBuffer = avlBuffer.slice(28);

            const countOneByteIO = avlBuffer.readUInt16BE(0);
            const oneByteIOChunk = avlBuffer.slice(0, 2 + countOneByteIO * 3);

            avlBuffer = avlBuffer.slice(2 + countOneByteIO * 3);

            const countTwoBytesIO = avlBuffer.readUInt16BE(0);
            const newCountTwoBytesIO = Buffer.alloc(2);
            newCountTwoBytesIO.writeUInt16BE(avlBuffer.readUInt16BE(0) + isFuelLevelDataAdded, 0);

            avlBuffer = avlBuffer.slice(2);

            const twoBytesIOChunk = isFuelLevelDataAdded === 1 ? Buffer.concat([newCountTwoBytesIO, fuelIO, fuelIOValue, avlBuffer.slice(0, countTwoBytesIO * 4)]) : Buffer.concat([newCountTwoBytesIO, avlBuffer.slice(0, countTwoBytesIO * 4)]);

            avlBuffer = avlBuffer.slice(countTwoBytesIO * 4);

            const countFourBytesIO = avlBuffer.readUInt16BE(0);
            const fourBytesIOChunk = avlBuffer.slice(0, 2 + countFourBytesIO * 6);

            avlBuffer = avlBuffer.slice(2 + countFourBytesIO * 6);

            const countEightBytesIO = avlBuffer.readUInt16BE(0);
            const eightBytesIOChunk = avlBuffer.slice(0, 2 + countEightBytesIO * 10);

            avlBuffer = avlBuffer.slice(2 + countEightBytesIO * 10);

            const countVariableBytes = avlBuffer.readUInt16BE(0);

            avlBuffer = avlBuffer.slice(2);

            const variableBytesIOChunk = [];
            for (let j = 0; j < countVariableBytes; j++) {
                const ioLength = avlBuffer.readUInt16BE(2);
                variableBytesIOChunk.push(avlBuffer.slice(0, 4 + ioLength));

                avlBuffer = avlBuffer.slice(4 + ioLength);
            }

            const chunk = Buffer.concat([startChunk, totalIO, oneByteIOChunk, twoBytesIOChunk, fourBytesIOChunk, eightBytesIOChunk, Buffer.from([0, countVariableBytes]), ...variableBytesIOChunk]);
            avlChunk.push(chunk);
        }

        const dataFieldLengthFinal = Buffer.alloc(4);
        dataFieldLengthFinal.writeUInt32BE(dataFieldLength + 4 * countFuelDataAdded, 0);

        const messageWithoutCrc = Buffer.concat([headerChunk, ...avlChunk, Buffer.from([numberOfData])]);
        const crcValue = Buffer.alloc(4);
        crcValue.writeUInt32BE(crc.crc16(messageWithoutCrc), 0);

        return Buffer.concat([Buffer.from([0x00, 0x00, 0x00, 0x00]), dataFieldLengthFinal, messageWithoutCrc, crcValue]);
    };
}

module.exports = TeltonikaHandler;