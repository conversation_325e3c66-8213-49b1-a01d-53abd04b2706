// npm install fs
// npm install crc
// npm i async-mutex

const net = require('net');
const { getDbInstance, createTableForIMEI, imeiLastRecords } = require('./db.js');
const db = getDbInstance();
const fs = require('fs');
const crc = require('crc');
const { Mutex } = require('async-mutex');  // Add this at the top
const dbWriteLock = new Mutex();

const destinations = [
    { name: 'Fleetnav-Teltonika_FMB920', host: '*************', port: 5005, },
    //{ name: 'iNav-Office', host: '**************', port: 6300 },
];

const commands = [
    //'deleterecords',
    'setparam 2007:ltfrb.inavcloud.com;2008:5542;1004:0;107:0;',
    //'cpureset',
];

let delayedImeis = [];
const lastImeiData = new Map();
const lastCommandSentTime = new Map(); // Track when commands were last sent to each IMEI
const imeiErrorCount = new Map(); // Track error counts per IMEI

let destinationConnectionId = 1;
let logCount = 10;
const IDLE_TIMEOUT = 30 * 1000;
const SQL_BATCH_INTERVAL = 10 * 1000;
const COMMAND_COOLDOWN = 120 * 1000; // 2 minutes in milliseconds
const MAX_ERRORS_PER_IMEI = 10; // Maximum number of errors to log per IMEI
const imeiLatLngTimeAcc = new Map();

const options = {
    timeZone: 'Asia/Manila',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
};

// Constants
const START_BIT_LOGIN = '000f';
const START_BIT_DATA = '00000000';
const CODEC_8E_MESSAGE_TYPE = 0x8E;
const CODEC_8_MESSAGE_TYPE = 0x08;
const CODEC_12_MESSAGE_TYPE = 0x0C;
const CODEC_13_MESSAGE_TYPE = 0x0D;
const COMMAND_MESSAGE = 0x05;
const COMMAND_RESPONSE = 0x06;


// Message types that need response
const MESSAGE_NEED_RESPONSE_TYPE = [
    START_BIT_LOGIN, CODEC_8_MESSAGE_TYPE, CODEC_8E_MESSAGE_TYPE
];

// Load the IO ID to name mapping from a text file
const ioNames = {};
const ioMappingData = fs.readFileSync('io_names.csv', 'utf8');
ioMappingData.split('\n').forEach(line => {
    const [ioId, ioName] = line.trim().split(',');
    ioNames[ioId] = ioName;
});


class TeltonikaHandler {
    constructor() {

        this.destinationSockets = new Map();
        this.sqlBatch = [];
        this.sqlBatchTimer = null;
        this.imei = '';
        this.data = '';

        this.initializeSQLBatch();
    }

    initializeSQLBatch() {
        this.sqlBatchTimer = setInterval(this.sqlLogBatch.bind(this), SQL_BATCH_INTERVAL);
    }

    async sqlLogBatch() {
        if (this.sqlBatch.length > 0 && this.imei) {
            await dbWriteLock.runExclusive(async () => {
                return new Promise((resolve, reject) => {
                    createTableForIMEI(db, this.imei);

                    const sanitizedImei = this.imei.replace(/[^a-zA-Z0-9_]/g, '');
                    const query = `INSERT INTO gps_data_${sanitizedImei} (
                        serverTimestamp,
                        gpsTimestamp,
                        latitude,
                        longitude,
                        speed,
                        course,
                        satelliteCount,
                        accCode
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

                    db.serialize(() => {
                        db.run("BEGIN TRANSACTION");
                        const stmt = db.prepare(query);

                        try {
                            for (const message of this.sqlBatch) {
                                if (message.messageType !== START_BIT_LOGIN) {
                                    stmt.run(message.data, (err) => {
                                        if (err) {
                                            console.error('Error inserting message', err.message);
                                        }
                                    });
                                }
                            }

                            stmt.finalize((err) => {
                                if (err) {
                                    console.error('Error finalizing statement:', err.message);
                                    db.run("ROLLBACK");
                                    reject(err);
                                } else {
                                    db.run("COMMIT", (err) => {
                                        if (err) {
                                            console.error('Error committing transaction:', err.message);
                                            db.run("ROLLBACK");
                                            reject(err);
                                        } else {
                                            this.sqlBatch = [];
                                            resolve();
                                        }
                                    });
                                }
                            });
                        } catch (err) {
                            console.error('Error in batch processing:', err);
                            stmt.finalize();
                            db.run("ROLLBACK");
                            reject(err);
                        }
                    });
                });
            }).catch((err) => {
                console.error('Database write failed:', err);
            });
        }
    }

    connectToDestinationSockets() {
        for (const destination of destinations) {
            const socket = new net.Socket();

            socket.connect(destination.port, destination.host, () => {
                socket.destination = destination;
                socket.connectionId = destinationConnectionId++;
                this.destinationSockets.set(socket.connectionId, socket);
                //console.log(`${this.imei} connected to ${destination.name}[${socket.connectionId}]`);
            });

            socket.on('error', err => {
                if (logCount !== 0) {
                    console.log(`Error connecting to ${destination.name}: ${err.message}`);
                    logCount--;
                }
                socket.destroy();
            });

            socket.on('close', () => {
                socket.removeAllListeners();
                this.destinationSockets.delete(socket.connectionId);
            })

            socket.setTimeout(IDLE_TIMEOUT, () => {
                socket.destroy();
            });
        }
    }

    async handle(deviceSocket, data, commandAndResponse) {

        //const serverTimestamp = new Date().toLocaleString('en-CA', options).replace(',', '');
        const serverTimestamp = new Date().toISOString();

        try {
            const parsedMessage = TeltonikaHandler.parseMessage(data);
            //console.log('Parsed message:', JSON.stringify(parsedMessage));

            if (parsedMessage.imei) this.imei = parsedMessage.imei;
            if (parsedMessage.data) this.data = JSON.stringify(parsedMessage.data);

            // If no this.imei, destroy the socket
            if (!this.imei) {
                deviceSocket.destroy();
                return;
            }

            // Insert message into SQLite database
            if ((parsedMessage.messageType === CODEC_8_MESSAGE_TYPE || parsedMessage.messageType === CODEC_8E_MESSAGE_TYPE)) {
                parsedMessage.data.forEach(dataPoint => {

                    // Store last records
                    const existingData = imeiLastRecords.get(this.imei) || {};
                    const updatedData = {
                        imei: this.imei,
                        serverTimestamp: serverTimestamp,
                        gpsTimestamp: parsedMessage.messageType === START_BIT_LOGIN ? existingData.gpsTimestamp : dataPoint.gpsTimestamp,
                        latitude: parsedMessage.messageType === START_BIT_LOGIN ? existingData.latitude : dataPoint.latitude,
                        longitude: parsedMessage.messageType === START_BIT_LOGIN ? existingData.longitude : dataPoint.longitude,
                        speed: parsedMessage.messageType === START_BIT_LOGIN ? existingData.speed : dataPoint.speed,
                        course: parsedMessage.messageType === START_BIT_LOGIN ? existingData.course : dataPoint.course,
                        satelliteCount: parsedMessage.messageType === START_BIT_LOGIN ? existingData.satelliteCount : dataPoint.satelliteCount,
                        accCode: parsedMessage.messageType === START_BIT_LOGIN ? existingData.accCode : dataPoint.io.Ignition
                    };

                    imeiLastRecords.set(this.imei, updatedData);

                    // Filter out GPS messages
                    const latLngTimeAcc = imeiLatLngTimeAcc.get(this.imei) || [0, 0, 0, 0];
                    const dist = Math.sqrt(Math.pow(latLngTimeAcc[0] - dataPoint.latitude, 2) + Math.pow(latLngTimeAcc[1] - dataPoint.longitude, 2)) * 111 * 1000;
                    const timeDiff = (new Date(dataPoint.gpsTimestamp) - new Date(latLngTimeAcc[2])) / 1000;
                    const isAccChanged = (dataPoint.io.Ignition !== latLngTimeAcc[3]);
                    const isLatLng = (dataPoint.latitude !== 0 && dataPoint.longitude !== 0);

                    if (
                        (dataPoint.gpsTimestamp &&
                            isLatLng &&
                            (dist > 50 || timeDiff > 3600 / 6 || isAccChanged)
                        ) || !imeiLatLngTimeAcc.has(this.imei)
                    ) {
                        if (dataPoint.eventIoId != 0) {
                            return
                        }
                        this.sqlBatch.push({
                            messageType: dataPoint.messageType,
                            data: [
                                serverTimestamp,
                                dataPoint.gpsTimestamp,
                                dataPoint.latitude,
                                dataPoint.longitude,
                                dataPoint.speed,
                                dataPoint.course,
                                dataPoint.satelliteCount,
                                dataPoint.io.Ignition
                            ]
                        });
                        imeiLatLngTimeAcc.set(
                            this.imei,
                            [parseFloat(dataPoint.latitude),
                            parseFloat(dataPoint.longitude),
                            dataPoint.gpsTimestamp,
                            dataPoint.io.Ignition]
                        );
                    }
                });
            }

            // Check if destination sockets already exist for this IMEI
            if (this.destinationSockets && this.destinationSockets.size === 0) {
                await new Promise((resolve) => {
                    this.connectToDestinationSockets();

                    // Check if all destination sockets are connected
                    const intervalId = setInterval(() => {
                        if (this.destinationSockets.size === destinations.length) {
                            clearInterval(intervalId);
                            clearTimeout(timeoutId);
                            // Only resolve the promise here, after sockets have connected
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to reject the promise if the condition isn't met in time
                    const timeoutId = setTimeout(() => {
                        clearInterval(intervalId);
                        resolve();
                    }, 5 * 1000)
                });
            }

            // Forward device data and destination response
            if (this.destinationSockets && this.destinationSockets.size > 0) {
                this.destinationSockets.forEach(socket => {

                    // Send device data to destination
                    if (socket && socket.writable) {
                        //console.log(`Send ${this.imei}[${deviceSocket.connectionId}] data to ${socket.destination.name}[${socket.connectionId}] ${data.toString('hex')}`);
                        socket.write(data, err => {
                            if (err) {
                                socket.destroy();
                                this.destinationSockets.delete(socket.connectionId);
                            }
                        });
                    } else {
                        this.destinationSockets.delete(socket.connectionId);
                        return; // Skip to the next socket
                    }

                    // Send destination response to device
                    /*socket.removeAllListeners('data');
                    socket.on('data', (response) => {
                        if (deviceSocket && deviceSocket.writable) {
                            //console.log(`${this.imei} received response from ${socket.destination.name}[${socket.connectionId}]`);
                            deviceSocket.write(response, err => {
                                if (err) {
                                    deviceSocket.destroy();
                                    //console.log(`Error sending response to ${this.imei} [${deviceSocket.connectionId}]`);
                                }
                            });
                        } else {
                            deviceSocket.destroy();
                            //console.log(`Error ${this.imei} not writable [${deviceSocket.connectionId}]`);
                        }
                    });*/
                });
            }

            // Send response to device
            if (this.isMessageNeedResponse(parsedMessage)) {
                const responseMessage = parsedMessage.response;
                //console.log(`Response to ${this.imei} [${deviceSocket.connectionId}]:`, responseMessage);
                if (deviceSocket && deviceSocket.writable) {
                    deviceSocket.write(responseMessage, (err) => {
                        if (err) {
                            deviceSocket.destroy();
                            console.log(`Error sending my response to ${this.imei}`);
                        }
                    });
                }
            }

            // Send command to device
            if (commandAndResponse && commandAndResponse.has(this.imei)) {
                const commandData = commandAndResponse.get(this.imei);
                if (!commandData.response && parsedMessage.messageType !== START_BIT_LOGIN) {
                    const currentTime = Date.now();
                    const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                    // Check if the cooldown period has passed since the last command was sent
                    if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                        const commandMessage = TeltonikaHandler.constructCommandMessage(commandData.command);
                        console.log(`Send command to ${this.imei} [${deviceSocket.connectionId}]:`, commandData.command);

                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.log(`Error sending command to ${this.imei}`);
                                }
                            });

                            // Update the last sent time for this IMEI
                            lastCommandSentTime.set(this.imei, currentTime);
                        }
                    } else {
                        console.log(`Command to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN/1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                    }
                }
            }

            // Store command response
            if (parsedMessage.responseToCommand) {
                const commandData = commandAndResponse.get(this.imei);
                if (commandData) {
                    commandData.response = parsedMessage.responseToCommand;
                    commandAndResponse.set(this.imei, commandData);
                    console.log(`Response to Command ${this.imei} [${deviceSocket.connectionId}]:`, commandData.response);
                }
            }

            // Send troubleshooting command to delayed device
            let isGsmReset = false;
            if (lastImeiData.get(this.imei) === data) isGsmReset = true;
            lastImeiData.set(this.imei, this.data);

            if (parsedMessage.gpsTimestamp) {
                const isDelay = (new Date() - new Date(parsedMessage.gpsTimestamp)) > 1 * 60 * 60 * 1000;
                if (isDelay) delayedImeis.push(this.imei);
            }

            if ((delayedImeis.includes(this.imei) || isGsmReset)) {
                const currentTime = Date.now();
                const lastSentTime = lastCommandSentTime.get(this.imei) || 0;

                // Check if the cooldown period has passed since the last command was sent
                if (currentTime - lastSentTime >= COMMAND_COOLDOWN) {
                    for (let i = 0; i < commands.length; i++) {
                        const commandMessage = TeltonikaHandler.constructCommandMessage(commands[i]);
                        console.log(`Send troubleshooting command to ${this.imei} [${deviceSocket.connectionId}]:`, commands[i]);
                        if (deviceSocket && deviceSocket.writable) {
                            deviceSocket.write(commandMessage, (err) => {
                                if (err) {
                                    deviceSocket.destroy();
                                    console.error(`Error sending troubleshooting command to ${this.imei}:`, err);
                                }
                            });
                        }
                        // Add delay before sending the next command
                        await new Promise(resolve => setTimeout(resolve, 100)); // add delay
                    }
                    // Update the last sent time for this IMEI
                    lastCommandSentTime.set(this.imei, currentTime);
                    delayedImeis = delayedImeis.filter(imei => imei !== this.imei);
                } else {
                    console.log(`Troubleshooting commands to ${this.imei} skipped - cooldown period (${COMMAND_COOLDOWN/1000}s) not elapsed. Last sent: ${new Date(lastSentTime).toISOString()}`);
                }
            }

        } catch (error) {
            console.error(`Teltonika Handle Error [${this.imei}]:`, error.message);
        }
    }

    cleanup() {
        if (this.sqlBatchTimer) {
            clearInterval(this.sqlBatchTimer);
        }

        if (this.sqlBatch.length > 0) {
            return this.sqlLogBatch()
                .catch(err => console.error('Error during final batch save:', err))
                .finally(() => {
                    this.destinationSockets.forEach(socket => {
                        socket.destroy();
                    });
                    this.destinationSockets.clear();
                });
        } else {
            this.destinationSockets.forEach(socket => {
                socket.destroy();
            });
            this.destinationSockets.clear();
            return Promise.resolve();
        }
    }


    // HELPER FUNCTIONS
    static pad = (number) => (number < 10 ? '0' : '') + number;
    static getMessageSegment = (message, start, end) => message.slice(start, end).toString('hex');
    static getAsciiSegment = (message, start, end) => message.slice(start, end).toString('ascii');
    isMessageNeedResponse = (parsedMessage) => MESSAGE_NEED_RESPONSE_TYPE.includes(parsedMessage.messageType);


    // Parse messages
    static parseMessage = (message) => {
        const startBitLogin = TeltonikaHandler.getMessageSegment(message, 0, 2);
        const startBitData = TeltonikaHandler.getMessageSegment(message, 0, 4);

        if (startBitLogin === START_BIT_LOGIN) {
            const messageType = startBitLogin;

            return {
                ...TeltonikaHandler.parseLoginMessage(message),
                messageType
            }

        } else if (startBitData === START_BIT_DATA) {
            const messageType = message[8];

            switch (messageType) {
                case CODEC_8_MESSAGE_TYPE:
                    //console.log('CODEC_8_MESSAGE_TYPE');
                    return {
                        ...TeltonikaHandler.parseCodec8Message(message),
                        messageType
                    };
                case CODEC_8E_MESSAGE_TYPE:
                    //console.log('CODEC_8E_MESSAGE_TYPE');
                    return {
                        ...TeltonikaHandler.parseCodec8ExtendedMessage(message),
                        messageType
                    };
                case CODEC_12_MESSAGE_TYPE:
                    return {
                        ...TeltonikaHandler.parseCommandResponse(message),
                        messageType
                    };
                case CODEC_13_MESSAGE_TYPE:
                    const payloadMessage = TeltonikaHandler.parseCodec13Message(message);

                    if (payloadMessage.payload.startsWith('*RFV')) {
                        // Extract fuel level data
                        const fuelLevel = TeltonikaHandler.extractFuelLevel(payloadMessage.payload);
                        return {
                            ...payloadMessage,
                            messageType,
                            fuelLevel
                        };

                    } else {
                        return {
                            ...payloadMessage,
                            messageType
                        };
                    }
            }
        } else {
            // Get current error count for this IMEI or initialize to 0
            const errorCount = imeiErrorCount.get(this.imei) || 0;

            // Only log if we haven't reached the maximum number of errors for this IMEI
            if (errorCount < MAX_ERRORS_PER_IMEI) {
                console.error(`Teltonika Handle Error [${this.imei}]:`, error.message);
                // Increment the error count for this IMEI
                imeiErrorCount.set(this.imei, errorCount + 1);
            }
        }
    };


    // Data	000F383636393037303538383034333531
    // IMEI	866907058804351
    static parseLoginMessage = (message) => {
        const packetLength = message[1]; // Length 000F
        const imei = message.slice(2, packetLength + 2).toString();
        const data = imei;

        return {
            imei,
            data,
            response: Buffer.from('01', 'hex')
        };
    }


    // Codec 8 message
    // 000000000000004f0801000001916fc729a80047bd62cd098ed2a70010012d0d0014001006ef01f0011503c8004501010108b5000fb60007423261180014cd3016ce524c43103f44001302f10000c92f10012f6c5d00010000b445
    static parseCodec8Message = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData = buffer.readUInt8(9);

        //console.log('dataFieldLength', dataFieldLength, 'codecId', codecId, 'numberOfData', numberOfData);

        const avlData = [];
        let bufferIO = [];
        let avlBuffer = buffer.slice(10);

        for (let i = 0; i < numberOfData; i++) {
            // Format the gpsTimestamp from 2023-03-20T23:31:09.000Z to 'YYYY-MM-DD HH:mm:ss'
            const unixTimestamp = Number(avlBuffer.readBigInt64BE());
            const rawTimestamp = new Date(unixTimestamp);
            const gpsTimestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
            const priority = avlBuffer.readUInt8(8);
            avlBuffer = avlBuffer.slice(9);
            //console.log('gpsTimestamp', gpsTimestamp, 'priority', priority);

            const longitude = avlBuffer.readInt32BE() / 10000000;
            const latitude = avlBuffer.readInt32BE(4) / 10000000;
            const altitude = avlBuffer.readInt16BE(8);
            const course = avlBuffer.readUInt16BE(10);
            const satelliteCount = avlBuffer.readUInt8(12);
            const speed = avlBuffer.readUInt16BE(13);
            avlBuffer = avlBuffer.slice(15);
            //console.log('longitude', longitude, 'latitude', latitude, 'altitude', altitude, 'course', course, 'satelliteCount', satelliteCount, 'speed', speed);

            const eventIoId = avlBuffer.readUInt8();
            const totalIO = avlBuffer.readUInt8(1);
            avlBuffer = avlBuffer.slice(2);
            //console.log('eventIoId', eventIoId, 'totalIO', totalIO);

            bufferIO = [];
            const countOneByteIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);
            //console.log('countOneByteIO', countOneByteIO);

            for (let j = 0; j < countOneByteIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt8(1);
                avlBuffer = avlBuffer.slice(2);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('OneByteIO', countOneByteIO);

            const countTwoBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countTwoBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt16BE(1);
                avlBuffer = avlBuffer.slice(3);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('TwoBytesIO', countTwoBytesIO);

            const countFourBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countFourBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt32BE(1);
                avlBuffer = avlBuffer.slice(5);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('FourBytesIO', countFourBytesIO);

            const countEightBytesIO = avlBuffer.readUInt8();
            avlBuffer = avlBuffer.slice(1);

            for (let j = 0; j < countEightBytesIO; j++) {
                let ioID = avlBuffer.readUInt8();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.slice(1, 9).toString('hex');
                avlBuffer = avlBuffer.slice(9);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('EightBytesIO', countEightBytesIO);

            const io = Object.assign({}, ...bufferIO);
            avlData.push({ gpsTimestamp, priority, longitude, latitude, altitude, course, satelliteCount, speed, eventIoId, totalIO, io });

            if (avlBuffer.length < (dataFieldLength - 3) / numberOfData) break;
        }

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');

        const data = avlData

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            data, //avlData,
            //numberOfData2,
            //crc16
            response: Buffer.from((numberOfData + '').padStart(8, '0'), 'hex')
        };
    }

    // Codec 8 extended message
    // 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
    static parseCodec8ExtendedMessage = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData = buffer.readUInt8(9);
        //console.log(`dataFieldLength: ${dataFieldLength}, codecId: ${codecId}, numberOfData1: ${numberOfData}`);

        const avlData = [];
        let bufferIO = [];
        let avlBuffer = buffer.slice(10);

        for (let i = 0; i < numberOfData; i++) {
            // Format the gpsTimestamp from 2023-03-20T23:31:09.000Z to 'YYYY-MM-DD HH:mm:ss'
            const unixTimestamp = Number(avlBuffer.readBigInt64BE());
            const rawTimestamp = new Date(unixTimestamp);
            const gpsTimestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
            const priority = avlBuffer.readUInt8(8);
            avlBuffer = avlBuffer.slice(9);
            //console.log(`gpsTimestamp: ${gpsTimestamp}, priority: ${priority}`);

            const longitude = avlBuffer.readInt32BE() / 10000000;
            const latitude = avlBuffer.readInt32BE(4) / 10000000;
            const altitude = avlBuffer.readInt16BE(8);
            const course = avlBuffer.readUInt16BE(10);
            const satelliteCount = avlBuffer.readUInt8(12);
            const speed = avlBuffer.readUInt16BE(13);
            avlBuffer = avlBuffer.slice(15);
            //console.log(`longitude: ${longitude}, latitude: ${latitude}, altitude: ${altitude}, course: ${course}, satelliteCount: ${satelliteCount}, speed: ${speed}`);

            const eventIoId = avlBuffer.readUInt16BE();
            const totalIO = avlBuffer.readUInt16BE(2);
            avlBuffer = avlBuffer.slice(4);
            //console.log(`eventIoId: ${eventIoId}, totalIO: ${totalIO}`);

            bufferIO = [];
            const countOneByteIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countOneByteIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt8(2);
                avlBuffer = avlBuffer.slice(3);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('OneByteIO', countOneByteIO);

            const countTwoBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countTwoBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt16BE(2);
                avlBuffer = avlBuffer.slice(4);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('TwoBytesIO', countTwoBytesIO);

            const countFourBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countFourBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.readUInt32BE(2);
                avlBuffer = avlBuffer.slice(6);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('FourBytesIO', countFourBytesIO);

            const countEightBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countEightBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioValue = avlBuffer.slice(2, 10).toString('hex');
                avlBuffer = avlBuffer.slice(10);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('EightBytesIO', countEightBytesIO);

            const countVariableBytesIO = avlBuffer.readUInt16BE();
            avlBuffer = avlBuffer.slice(2);

            for (let j = 0; j < countVariableBytesIO; j++) {
                let ioID = avlBuffer.readUInt16BE();
                if (ioNames[ioID]) {
                    ioID = ioNames[ioID];
                }
                const ioLength = avlBuffer.readUInt16BE(2);
                const ioValue = avlBuffer.slice(4, 4 + ioLength).toString('hex');
                avlBuffer = avlBuffer.slice(4 + ioLength);
                bufferIO.push({ [ioID]: ioValue });
            }
            //console.log('VariableBytesIO', countVariableBytesIO);

            const io = Object.assign({}, ...bufferIO);

            avlData.push({ gpsTimestamp, priority, longitude, latitude, altitude, course, satelliteCount, speed, eventIoId, totalIO, io });

            if (avlBuffer.length < (dataFieldLength - 3) / numberOfData) break;
        }

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');

        const data = avlData

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            data, //avlData,
            //numberOfData2,
            //crc16
            response: Buffer.from((numberOfData + '').padStart(8, '0'), 'hex')
        };
    }

    // Function to parse Codec 13 message
    // *RFV01077.84B1
    // 000000000000001c0d01060000001466ff90322a52465630313037372e383442310d0a0100008822
    static parseCodec13Message = (message) => {
        const buffer = Buffer.from(message, 'hex');

        const dataFieldLength = buffer.readUInt32BE(4);
        const codecId = buffer.readUInt8(8);
        const numberOfData1 = buffer.readUInt8(9);
        //console.log(dataFieldLength, codecId, numberOfData1);

        const payloadType = buffer.readUInt8(10);
        const payloadLength = buffer.readUInt32BE(11);
        //console.log(payloadType, payloadLength);

        const unixTimestamp = buffer.readUInt32BE(15);
        const rawTimestamp = new Date(unixTimestamp * 1000);
        const timestamp = `${rawTimestamp.getUTCFullYear()}-${TeltonikaHandler.pad(rawTimestamp.getUTCMonth() + 1)}-${TeltonikaHandler.pad(rawTimestamp.getUTCDate())} ${TeltonikaHandler.pad(rawTimestamp.getUTCHours())}:${TeltonikaHandler.pad(rawTimestamp.getUTCMinutes())}:${TeltonikaHandler.pad(rawTimestamp.getUTCSeconds())}`;
        const payload = (buffer.slice(19, 19 + payloadLength)).toString().toString().replace(/\r\n.+/g, '');  // RFV01065.84AE\r\n\u0001\u0000\u0000�;
        //console.log(timestamp,payload);

        const numberOfData2 = buffer.slice(-5, -4).toString('hex');
        const crc16 = buffer.slice(-4).toString('hex');
        //console.log(numberOfData2, crc16);

        const data = `${payloadType}, ${timestamp}, ${payload}`;

        return {
            //dataFieldLength,
            //codecId,
            //numberOfData1,
            //payloadType,
            //payloadLength,
            timestamp,
            payload,
            data,
            //numberOfData2,
            //crc16
        };
    }

    // Function to extract fuel level data from the CLS2 payload
    // *RFV01077.84B1
    static extractFuelLevel(payload) {
        const fuelLevelData = payload.slice(7, 5);
        return fuelLevelData;
    }


    // Function to construct a Commmand message
    // Command = getinfo
    // Command message = 000000000000000F0C010500000007676574696E666F0100004312
    // 000000000000000f0c010500000007676574696e666f0100004312
    static constructCommandMessage = (command) => {

        const startBit = Buffer.alloc(4, 0x00);
        //console.log(startBit.toString('hex'));
        const commandBuffer = Buffer.from(command);

        const packetLength = 8 + commandBuffer.length;
        const messageLength = Buffer.alloc(4);
        messageLength.writeUInt32BE(packetLength);
        //console.log(messageLength.toString('hex'));

        const messageType = Buffer.from([CODEC_12_MESSAGE_TYPE]);
        const commandCount = Buffer.from([0x01], 'hex');
        const commandType = Buffer.from([COMMAND_MESSAGE]);
        const commmandLength = Buffer.alloc(4);
        commmandLength.writeUInt32BE(commandBuffer.length);

        const messageWithoutCrc = Buffer.concat([messageType, commandCount, commandType, commmandLength, commandBuffer, commandCount]);
        //console.log(messageWithoutCrc.toString('hex'));

        const crcValue = Buffer.alloc(4);
        crcValue.writeUint32BE(crc.crc16(messageWithoutCrc));

        return Buffer.concat([startBit, messageLength, messageWithoutCrc, crcValue]);
    }

    // Function to parse command response message
    // Param ID:2004 Value:fmb.inavcloud.com+
    // message: 000000000000002d0c010600000025506172616d2049443a323030342056616c75653a666d622e696e6176636c6f75642e636f6d01000095ab
    static parseCommandResponse = (message) => {

        const commandType = TeltonikaHandler.getMessageSegment(message, 10, 11);
        const commmandLength = parseInt(TeltonikaHandler.getMessageSegment(message, 11, 15), 16);
        //console.log(commandType, commmandLength, TeltonikaHandler.getMessageSegment(message, 15, 15 + commmandLength));
        const data = TeltonikaHandler.getAsciiSegment(message, 15, 15 + commmandLength);
        //console.log(data);

        const responseToCommand = data;

        return {
            commandType,
            responseToCommand
        };
    }
}
module.exports = TeltonikaHandler;